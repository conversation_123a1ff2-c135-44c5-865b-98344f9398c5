//
//  ARMCodeGenerator.m
//  ArmRunDemo
//
//  Created by <PERSON> on 2025/7/4.
//  Copyright © 2025年 Ming Dai. All rights reserved.
//

#import "ARMCodeGenerator.h"

@interface ARMCodeGenerator ()
@property (nonatomic, strong) NSMutableDictionary *instructionCache;
@end

@implementation ARMCodeGenerator

+ (instancetype)sharedInstance {
    static ARMCodeGenerator *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[ARMCodeGenerator alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _instructionCache = [[NSMutableDictionary alloc] init];
    }
    return self;
}

- (NSData *)generateWrapperCodeForClass:(Class)targetClass
                               selector:(SEL)selector
                       isInstanceMethod:(BOOL)isInstanceMethod {
    
    NSLog(@"🔧 生成ARM包装代码: %@.%@", NSStringFromClass(targetClass), NSStringFromSelector(selector));
    
    // 获取方法签名
    NSMethodSignature *signature = isInstanceMethod ?
        [targetClass instanceMethodSignatureForSelector:selector] :
        [targetClass methodSignatureForSelector:selector];
    
    if (!signature) {
        NSLog(@"❌ 无法获取方法签名");
        return nil;
    }
    
    // 获取方法实现地址
    Method method = isInstanceMethod ?
        class_getInstanceMethod(targetClass, selector) :
        class_getClassMethod(targetClass, selector);
    
    if (!method) {
        NSLog(@"❌ 无法获取方法实现");
        return nil;
    }
    
    IMP implementation = method_getImplementation(method);
    uintptr_t targetAddress = (uintptr_t)implementation;
    
    NSLog(@"📍 目标方法地址: 0x%lx", targetAddress);
    
    // 生成包装代码
    NSMutableData *wrapperCode = [[NSMutableData alloc] init];
    
    // 1. 保存寄存器状态
    uint32_t pushInstruction = [self generatePUSHInstruction:@[@(ARMRegisterR4), @(ARMRegisterR5), @(ARMRegisterLR)]];
    [wrapperCode appendBytes:&pushInstruction length:sizeof(pushInstruction)];
    
    // 2. 生成参数传递代码
    NSData *argCode = [self generateArgumentPassingCodeForSignature:signature];
    if (argCode) {
        [wrapperCode appendData:argCode];
    }
    
    // 3. 调用原始方法
    NSData *callCode = [self generateMethodCallCode:signature targetAddress:targetAddress];
    if (callCode) {
        [wrapperCode appendData:callCode];
    }
    
    // 4. 处理返回值
    NSString *returnType = [NSString stringWithUTF8String:[signature methodReturnType]];
    NSData *returnCode = [self generateReturnValueCode:returnType];
    if (returnCode) {
        [wrapperCode appendData:returnCode];
    }
    
    // 5. 恢复寄存器状态并返回
    uint32_t popInstruction = [self generatePOPInstruction:@[@(ARMRegisterR4), @(ARMRegisterR5), @(ARMRegisterPC)]];
    [wrapperCode appendBytes:&popInstruction length:sizeof(popInstruction)];
    
    NSLog(@"✅ ARM包装代码生成完成，大小: %lu字节", (unsigned long)wrapperCode.length);
    return [wrapperCode copy];
}

- (NSData *)generateArgumentPassingCodeForSignature:(NSMethodSignature *)signature {
    NSMutableData *argCode = [[NSMutableData alloc] init];
    NSUInteger argumentCount = [signature numberOfArguments];
    
    NSLog(@"📝 生成参数传递代码，参数数量: %lu", (unsigned long)argumentCount);
    
    // 跳过self和_cmd参数，从第3个参数开始
    for (NSUInteger i = 2; i < argumentCount && i < 6; i++) { // ARM64最多4个寄存器参数
        const char *argType = [signature getArgumentTypeAtIndex:i];
        ARMRegister targetReg = (ARMRegister)(i - 2); // r0, r1, r2, r3
        
        if (strcmp(argType, @encode(int)) == 0 || 
            strcmp(argType, @encode(NSInteger)) == 0 ||
            strcmp(argType, @encode(long)) == 0) {
            // 整数参数：从栈中加载到寄存器
            // 简化实现：假设参数已经在正确的寄存器中
            NSLog(@"   参数%lu: 整数类型 -> r%d", (unsigned long)i-2, targetReg);
        } else if (strcmp(argType, @encode(float)) == 0 || 
                   strcmp(argType, @encode(double)) == 0) {
            // 浮点参数：需要特殊处理
            NSLog(@"   参数%lu: 浮点类型 -> r%d", (unsigned long)i-2, targetReg);
        } else {
            // 指针参数
            NSLog(@"   参数%lu: 指针类型 -> r%d", (unsigned long)i-2, targetReg);
        }
    }
    
    return [argCode copy];
}

- (NSData *)generateMethodCallCode:(NSMethodSignature *)methodSignature
                     targetAddress:(uintptr_t)targetAddress {
    NSMutableData *callCode = [[NSMutableData alloc] init];
    
    // 生成BL指令调用目标方法
    // 注意：这里简化处理，实际需要计算相对偏移
    uint32_t blInstruction = [self generateBLInstruction:(uint32_t)targetAddress currentAddress:0x1000];
    [callCode appendBytes:&blInstruction length:sizeof(blInstruction)];
    
    NSLog(@"📞 生成方法调用代码，目标地址: 0x%lx", targetAddress);
    return [callCode copy];
}

- (NSData *)generateArgumentPassingCode:(NSArray<NSString *> *)argumentTypes {
    NSMutableData *argCode = [[NSMutableData alloc] init];
    
    for (NSUInteger i = 0; i < argumentTypes.count && i < 4; i++) {
        NSString *argType = argumentTypes[i];
        ARMRegister targetReg = (ARMRegister)i;
        
        // 根据参数类型生成相应的传递代码
        if ([argType isEqualToString:@"i"] || [argType isEqualToString:@"l"]) {
            // 整数参数
            uint32_t movInstruction = [self generateMOVInstruction:targetReg value:0];
            [argCode appendBytes:&movInstruction length:sizeof(movInstruction)];
        }
    }
    
    return [argCode copy];
}

- (NSData *)generateReturnValueCode:(NSString *)returnType {
    NSMutableData *returnCode = [[NSMutableData alloc] init];
    
    if ([returnType isEqualToString:@"v"]) {
        // void返回类型，无需处理
        NSLog(@"📤 返回类型: void");
    } else if ([returnType isEqualToString:@"i"] || [returnType isEqualToString:@"l"]) {
        // 整数返回类型，结果在r0中
        NSLog(@"📤 返回类型: 整数 (r0)");
    } else if ([returnType isEqualToString:@"f"] || [returnType isEqualToString:@"d"]) {
        // 浮点返回类型
        NSLog(@"📤 返回类型: 浮点");
    } else {
        // 指针返回类型
        NSLog(@"📤 返回类型: 指针 (r0)");
    }
    
    return [returnCode copy];
}

- (uint32_t)generateInstruction:(ARMInstructionType)type operands:(NSArray *)operands {
    switch (type) {
        case ARMInstructionTypeMove:
            if (operands.count >= 2) {
                return [self generateMOVInstruction:[operands[0] unsignedIntValue] 
                                              value:[operands[1] unsignedIntValue]];
            }
            break;
        case ARMInstructionTypeAdd:
            if (operands.count >= 3) {
                return [self generateADDInstruction:[operands[0] unsignedIntValue]
                                            srcReg1:[operands[1] unsignedIntValue]
                                            srcReg2:[operands[2] unsignedIntValue]];
            }
            break;
        default:
            NSLog(@"⚠️ 不支持的指令类型: %lu", (unsigned long)type);
            break;
    }
    return 0;
}

- (uint32_t)generateMOVInstruction:(ARMRegister)destReg value:(uint32_t)value {
    // Thumb模式的MOV立即数指令: 001 00 ddd iiiiiiii
    // 其中ddd是目标寄存器(0-7)，iiiiiiii是8位立即数
    if (destReg > 7 || value > 255) {
        NSLog(@"⚠️ MOV指令参数超出范围: reg=%d, value=%u", destReg, value);
        return 0;
    }
    
    uint16_t instruction = 0x2000 | (destReg << 8) | (value & 0xFF);
    NSLog(@"🔧 生成MOV指令: mov r%d, #%u -> 0x%04X", destReg, value, instruction);
    return instruction;
}

- (uint32_t)generateADDInstruction:(ARMRegister)destReg
                           srcReg1:(ARMRegister)srcReg1
                           srcReg2:(ARMRegister)srcReg2 {
    // Thumb模式的ADD寄存器指令: 0001 100 mmm nnn ddd
    // 其中mmm是srcReg2，nnn是srcReg1，ddd是destReg
    if (destReg > 7 || srcReg1 > 7 || srcReg2 > 7) {
        NSLog(@"⚠️ ADD指令寄存器超出范围");
        return 0;
    }
    
    uint16_t instruction = 0x1800 | (srcReg2 << 6) | (srcReg1 << 3) | destReg;
    NSLog(@"🔧 生成ADD指令: add r%d, r%d, r%d -> 0x%04X", destReg, srcReg1, srcReg2, instruction);
    return instruction;
}

- (uint32_t)generateBLInstruction:(uint32_t)targetAddress currentAddress:(uint32_t)currentAddress {
    // 简化的BL指令生成
    // 实际实现需要计算相对偏移并处理Thumb/ARM模式切换
    int32_t offset = (int32_t)(targetAddress - currentAddress - 4);
    
    // Thumb BL指令是32位指令，这里简化为16位
    uint16_t instruction = 0xF000; // 简化的BL指令前缀
    NSLog(@"🔧 生成BL指令: bl 0x%X (offset: %d) -> 0x%04X", targetAddress, offset, instruction);
    return instruction;
}

- (uint32_t)generateBXInstruction:(ARMRegister)reg {
    // Thumb BX指令: 010001 11 0 mmmm 000
    if (reg > 15) {
        NSLog(@"⚠️ BX指令寄存器超出范围");
        return 0;
    }
    
    uint16_t instruction = 0x4700 | (reg << 3);
    NSLog(@"🔧 生成BX指令: bx r%d -> 0x%04X", reg, instruction);
    return instruction;
}

- (uint32_t)generatePUSHInstruction:(NSArray<NSNumber *> *)registers {
    // Thumb PUSH指令: 1011 010 M register_list
    uint16_t instruction = 0xB400;
    uint8_t regList = 0;
    
    for (NSNumber *regNum in registers) {
        ARMRegister reg = [regNum unsignedIntValue];
        if (reg == ARMRegisterLR) {
            instruction |= 0x0100; // M位，表示包含LR
        } else if (reg <= 7) {
            regList |= (1 << reg);
        }
    }
    
    instruction |= regList;
    NSLog(@"🔧 生成PUSH指令: push {...} -> 0x%04X", instruction);
    return instruction;
}

- (uint32_t)generatePOPInstruction:(NSArray<NSNumber *> *)registers {
    // Thumb POP指令: 1011 110 P register_list
    uint16_t instruction = 0xBC00;
    uint8_t regList = 0;
    
    for (NSNumber *regNum in registers) {
        ARMRegister reg = [regNum unsignedIntValue];
        if (reg == ARMRegisterPC) {
            instruction |= 0x0100; // P位，表示包含PC
        } else if (reg <= 7) {
            regList |= (1 << reg);
        }
    }
    
    instruction |= regList;
    NSLog(@"🔧 生成POP指令: pop {...} -> 0x%04X", instruction);
    return instruction;
}

- (NSData *)assembleCode:(NSString *)assembly {
    // 简化的汇编器实现
    NSLog(@"🔧 汇编代码: %@", assembly);
    
    // 这里应该实现完整的汇编器，暂时返回空数据
    return [[NSData alloc] init];
}

- (NSString *)disassembleCode:(NSData *)machineCode {
    // 简化的反汇编器实现
    const uint16_t *instructions = (const uint16_t *)machineCode.bytes;
    NSUInteger instructionCount = machineCode.length / sizeof(uint16_t);
    
    NSMutableString *assembly = [[NSMutableString alloc] init];
    
    for (NSUInteger i = 0; i < instructionCount; i++) {
        uint16_t instruction = instructions[i];
        NSString *disasm = [self disassembleInstruction:instruction];
        [assembly appendFormat:@"%04lX: %@\n", (unsigned long)i * 2, disasm];
    }
    
    return [assembly copy];
}

- (NSString *)disassembleInstruction:(uint16_t)instruction {
    if ((instruction & 0xF800) == 0x2000) {
        uint8_t rd = (instruction >> 8) & 0x7;
        uint8_t imm = instruction & 0xFF;
        return [NSString stringWithFormat:@"mov r%u, #%u", rd, imm];
    } else if ((instruction & 0xFE00) == 0x1800) {
        uint8_t rd = instruction & 0x7;
        uint8_t rn = (instruction >> 3) & 0x7;
        uint8_t rm = (instruction >> 6) & 0x7;
        return [NSString stringWithFormat:@"add r%u, r%u, r%u", rd, rn, rm];
    } else if (instruction == 0x0088) {
        return @"lsls r0, r1, #2";
    } else {
        return [NSString stringWithFormat:@"unknown (0x%04X)", instruction];
    }
}

- (BOOL)validateARMCode:(NSData *)machineCode {
    if (!machineCode || machineCode.length == 0) {
        return NO;
    }
    
    // 检查长度是否为偶数（Thumb指令为16位对齐）
    if (machineCode.length % 2 != 0) {
        NSLog(@"⚠️ ARM代码长度不是16位对齐");
        return NO;
    }
    
    // 简单的指令有效性检查
    const uint16_t *instructions = (const uint16_t *)machineCode.bytes;
    NSUInteger instructionCount = machineCode.length / sizeof(uint16_t);
    
    for (NSUInteger i = 0; i < instructionCount; i++) {
        uint16_t instruction = instructions[i];
        // 检查是否为有效的Thumb指令
        if (instruction == 0x0000) {
            NSLog(@"⚠️ 发现无效指令: 0x0000 at offset %lu", (unsigned long)i * 2);
            return NO;
        }
    }
    
    NSLog(@"✅ ARM代码验证通过");
    return YES;
}

- (NSDictionary *)getCallingConvention:(NSMethodSignature *)methodSignature {
    NSUInteger argumentCount = [methodSignature numberOfArguments];
    NSMutableArray *registerArgs = [[NSMutableArray alloc] init];
    NSMutableArray *stackArgs = [[NSMutableArray alloc] init];
    
    // ARM64 AAPCS: 前8个整数参数使用x0-x7，前8个浮点参数使用v0-v7
    for (NSUInteger i = 2; i < argumentCount; i++) { // 跳过self和_cmd
        const char *argType = [methodSignature getArgumentTypeAtIndex:i];
        NSUInteger argIndex = i - 2;
        
        if (argIndex < 4) { // 前4个参数使用寄存器
            [registerArgs addObject:@{
                @"index": @(argIndex),
                @"register": [NSString stringWithFormat:@"r%lu", (unsigned long)argIndex],
                @"type": [NSString stringWithUTF8String:argType]
            }];
        } else { // 其余参数使用栈
            [stackArgs addObject:@{
                @"index": @(argIndex),
                @"stackOffset": @((argIndex - 4) * 4),
                @"type": [NSString stringWithUTF8String:argType]
            }];
        }
    }
    
    const char *returnType = [methodSignature methodReturnType];
    
    return @{
        @"registerArguments": registerArgs,
        @"stackArguments": stackArgs,
        @"returnType": [NSString stringWithUTF8String:returnType],
        @"returnRegister": @"r0"
    };
}

@end
