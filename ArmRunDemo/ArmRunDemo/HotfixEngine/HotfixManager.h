//
//  HotfixManager.h
//  ArmRunDemo
//
//  Created by <PERSON> on 2025/7/4.
//  Copyright © 2025年 Ming Dai. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * 补丁状态
 */
typedef NS_ENUM(NSUInteger, HotfixPatchStatus) {
    HotfixPatchStatusPending,    // 待应用
    HotfixPatchStatusApplied,    // 已应用
    HotfixPatchStatusFailed,     // 应用失败
    HotfixPatchStatusRolledBack  // 已回滚
};

/**
 * 补丁类型
 */
typedef NS_ENUM(NSUInteger, HotfixPatchType) {
    HotfixPatchTypeMethodReplace,  // 方法替换
    HotfixPatchTypeMethodHook,     // 方法Hook
    HotfixPatchTypeCodeInjection,  // 代码注入
    HotfixPatchTypeBugfix,         // Bug修复
    HotfixPatchTypeFeature         // 功能增强
};

/**
 * 补丁信息
 */
@interface HotfixPatch : NSObject
@property (nonatomic, strong) NSString *patchId;
@property (nonatomic, strong) NSString *version;
@property (nonatomic, strong) NSString *name;
@property (nonatomic, strong) NSString *description;
@property (nonatomic, assign) HotfixPatchType type;
@property (nonatomic, assign) HotfixPatchStatus status;
@property (nonatomic, strong) NSString *targetClass;
@property (nonatomic, strong) NSString *targetMethod;
@property (nonatomic, assign) BOOL isInstanceMethod;
@property (nonatomic, strong) NSData *armCode;
@property (nonatomic, strong) NSString *methodSignature;
@property (nonatomic, strong) NSDate *createTime;
@property (nonatomic, strong) NSDate *applyTime;
@property (nonatomic, strong) NSDictionary *metadata;
@property (nonatomic, strong) NSString *hash;
@end

/**
 * 热修复管理器
 * 管理补丁的加载、应用、回滚和版本控制
 */
@interface HotfixManager : NSObject

/**
 * 单例实例
 */
+ (instancetype)sharedInstance;

/**
 * 初始化热修复管理器
 * @return 是否初始化成功
 */
- (BOOL)initialize;

/**
 * 从JSON数据加载补丁
 * @param jsonData JSON补丁数据
 * @return 补丁对象，失败返回nil
 */
- (HotfixPatch * _Nullable)loadPatchFromJSON:(NSData *)jsonData;

/**
 * 从文件加载补丁
 * @param filePath 补丁文件路径
 * @return 补丁对象，失败返回nil
 */
- (HotfixPatch * _Nullable)loadPatchFromFile:(NSString *)filePath;

/**
 * 应用补丁
 * @param patch 补丁对象
 * @return 是否应用成功
 */
- (BOOL)applyPatch:(HotfixPatch *)patch;

/**
 * 回滚补丁
 * @param patchId 补丁ID
 * @return 是否回滚成功
 */
- (BOOL)rollbackPatch:(NSString *)patchId;

/**
 * 获取补丁信息
 * @param patchId 补丁ID
 * @return 补丁对象，不存在返回nil
 */
- (HotfixPatch * _Nullable)getPatch:(NSString *)patchId;

/**
 * 获取所有补丁
 * @return 补丁数组
 */
- (NSArray<HotfixPatch *> *)getAllPatches;

/**
 * 获取已应用的补丁
 * @return 已应用补丁数组
 */
- (NSArray<HotfixPatch *> *)getAppliedPatches;

/**
 * 检查补丁是否已应用
 * @param patchId 补丁ID
 * @return 是否已应用
 */
- (BOOL)isPatchApplied:(NSString *)patchId;

/**
 * 验证补丁
 * @param patch 补丁对象
 * @return 验证结果字典
 */
- (NSDictionary *)validatePatch:(HotfixPatch *)patch;

/**
 * 检查补丁冲突
 * @param patch 补丁对象
 * @return 冲突的补丁数组
 */
- (NSArray<HotfixPatch *> *)checkPatchConflicts:(HotfixPatch *)patch;

/**
 * 移除补丁
 * @param patchId 补丁ID
 * @return 是否移除成功
 */
- (BOOL)removePatch:(NSString *)patchId;

/**
 * 清理所有补丁
 */
- (void)clearAllPatches;

/**
 * 导出补丁配置
 * @return JSON数据
 */
- (NSData * _Nullable)exportPatchConfiguration;

/**
 * 导入补丁配置
 * @param jsonData JSON配置数据
 * @return 是否导入成功
 */
- (BOOL)importPatchConfiguration:(NSData *)jsonData;

/**
 * 获取热修复统计信息
 * @return 统计信息字典
 */
- (NSDictionary *)getHotfixStats;

/**
 * 设置调试模式
 * @param enabled 是否启用调试
 */
- (void)setDebugMode:(BOOL)enabled;

/**
 * 启用/禁用补丁
 * @param patchId 补丁ID
 * @param enabled 是否启用
 * @return 是否操作成功
 */
- (BOOL)setPatchEnabled:(NSString *)patchId enabled:(BOOL)enabled;

/**
 * 批量应用补丁
 * @param patches 补丁数组
 * @return 应用结果字典
 */
- (NSDictionary *)applyPatches:(NSArray<HotfixPatch *> *)patches;

/**
 * 批量回滚补丁
 * @param patchIds 补丁ID数组
 * @return 回滚结果字典
 */
- (NSDictionary *)rollbackPatches:(NSArray<NSString *> *)patchIds;

/**
 * 创建补丁备份
 * @param patchId 补丁ID
 * @return 备份标识符
 */
- (NSString * _Nullable)createPatchBackup:(NSString *)patchId;

/**
 * 从备份恢复补丁
 * @param backupId 备份标识符
 * @return 是否恢复成功
 */
- (BOOL)restoreFromBackup:(NSString *)backupId;

@end

NS_ASSUME_NONNULL_END
