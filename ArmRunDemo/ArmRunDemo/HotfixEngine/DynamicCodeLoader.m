//
//  DynamicCodeLoader.m
//  ArmRunDemo
//
//  Created by <PERSON> on 2025/7/4.
//  Copyright © 2025年 Ming Dai. All rights reserved.
//

#import "DynamicCodeLoader.h"
#import <sys/mman.h>
#import <CommonCrypto/CommonDigest.h>

@implementation CodeSegmentInfo
@end

@interface DynamicCodeLoader ()
@property (nonatomic, strong) NSMutableDictionary<NSString *, CodeSegmentInfo *> *loadedSegments;
@property (nonatomic, strong) NSMutableSet<NSString *> *dangerousInstructions;
@property (nonatomic, assign) size_t totalAllocatedMemory;
@end

@implementation DynamicCodeLoader

+ (instancetype)sharedInstance {
    static DynamicCodeLoader *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[DynamicCodeLoader alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _loadedSegments = [[NSMutableDictionary alloc] init];
        _totalAllocatedMemory = 0;
        [self initializeDangerousInstructions];
    }
    return self;
}

- (void)initializeDangerousInstructions {
    // 初始化危险指令列表
    _dangerousInstructions = [[NSMutableSet alloc] initWithArray:@[
        @"svc",    // 系统调用
        @"hvc",    // 虚拟化调用
        @"smc",    // 安全监控调用
        @"brk",    // 断点
        @"hlt",    // 停机
        @"wfi",    // 等待中断
        @"wfe",    // 等待事件
        @"yield",  // 让出处理器
        @"dmb",    // 数据内存屏障
        @"dsb",    // 数据同步屏障
        @"isb"     // 指令同步屏障
    ]];
}

- (CodeSegmentInfo *)loadCode:(NSData *)machineCode
                   identifier:(NSString *)identifier
                  permissions:(CodePermission)permissions {
    
    if (!machineCode || !identifier || machineCode.length == 0) {
        NSLog(@"❌ 代码加载参数无效");
        return nil;
    }
    
    // 检查是否已存在
    if (_loadedSegments[identifier]) {
        NSLog(@"⚠️ 代码段已存在: %@", identifier);
        return _loadedSegments[identifier];
    }
    
    NSLog(@"🔄 开始加载代码段: %@, 大小: %lu字节", identifier, (unsigned long)machineCode.length);
    
    // 验证代码安全性
    NSDictionary *securityResult = [self validateCodeSecurity:machineCode];
    if (![securityResult[@"safe"] boolValue]) {
        NSLog(@"❌ 代码安全验证失败: %@", securityResult[@"reason"]);
        return nil;
    }
    
    // 分配内存
    size_t codeSize = machineCode.length;
    size_t alignedSize = (codeSize + 4095) & ~4095; // 4KB对齐
    
    void *codeMemory = mmap(NULL, alignedSize, PROT_READ | PROT_WRITE, 
                           MAP_PRIVATE | MAP_ANONYMOUS, -1, 0);
    
    if (codeMemory == MAP_FAILED) {
        NSLog(@"❌ 内存分配失败: %s", strerror(errno));
        return nil;
    }
    
    // 复制代码到分配的内存
    memcpy(codeMemory, machineCode.bytes, codeSize);
    
    // 设置内存权限
    int protFlags = 0;
    if (permissions & CodePermissionRead) protFlags |= PROT_READ;
    if (permissions & CodePermissionWrite) protFlags |= PROT_WRITE;
    if (permissions & CodePermissionExecute) protFlags |= PROT_EXEC;
    
    if (mprotect(codeMemory, alignedSize, protFlags) != 0) {
        NSLog(@"❌ 设置内存权限失败: %s", strerror(errno));
        munmap(codeMemory, alignedSize);
        return nil;
    }
    
    // 创建代码段信息
    CodeSegmentInfo *segmentInfo = [[CodeSegmentInfo alloc] init];
    segmentInfo.identifier = identifier;
    segmentInfo.machineCode = machineCode;
    segmentInfo.baseAddress = codeMemory;
    segmentInfo.size = alignedSize;
    segmentInfo.permissions = permissions;
    segmentInfo.status = CodeLoadStatusLoaded;
    segmentInfo.loadTime = [NSDate date];
    segmentInfo.enabled = YES;
    segmentInfo.metadata = @{
        @"originalSize": @(codeSize),
        @"alignedSize": @(alignedSize),
        @"hash": [self calculateCodeHash:machineCode]
    };
    
    // 保存到已加载列表
    _loadedSegments[identifier] = segmentInfo;
    _totalAllocatedMemory += alignedSize;
    
    NSLog(@"✅ 代码段加载成功: %@, 地址: %p", identifier, codeMemory);
    return segmentInfo;
}

- (CodeSegmentInfo *)loadCodeFromFile:(NSString *)filePath
                           identifier:(NSString *)identifier
                          permissions:(CodePermission)permissions {
    
    NSData *fileData = [NSData dataWithContentsOfFile:filePath];
    if (!fileData) {
        NSLog(@"❌ 无法读取文件: %@", filePath);
        return nil;
    }
    
    NSLog(@"📁 从文件加载代码: %@", filePath);
    return [self loadCode:fileData identifier:identifier permissions:permissions];
}

- (BOOL)unloadCode:(NSString *)identifier {
    CodeSegmentInfo *segmentInfo = _loadedSegments[identifier];
    if (!segmentInfo) {
        NSLog(@"⚠️ 代码段不存在: %@", identifier);
        return NO;
    }
    
    // 释放内存
    if (segmentInfo.baseAddress && segmentInfo.size > 0) {
        if (munmap(segmentInfo.baseAddress, segmentInfo.size) != 0) {
            NSLog(@"❌ 释放内存失败: %s", strerror(errno));
            return NO;
        }
        _totalAllocatedMemory -= segmentInfo.size;
    }
    
    // 从列表中移除
    [_loadedSegments removeObjectForKey:identifier];
    
    NSLog(@"✅ 代码段卸载成功: %@", identifier);
    return YES;
}

- (CodeSegmentInfo *)getCodeSegmentInfo:(NSString *)identifier {
    return _loadedSegments[identifier];
}

- (void *)getExecutionAddress:(NSString *)identifier {
    CodeSegmentInfo *segmentInfo = _loadedSegments[identifier];
    if (!segmentInfo || !segmentInfo.enabled) {
        return NULL;
    }
    
    if (!(segmentInfo.permissions & CodePermissionExecute)) {
        NSLog(@"⚠️ 代码段不可执行: %@", identifier);
        return NULL;
    }
    
    return segmentInfo.baseAddress;
}

- (BOOL)setCodePermissions:(NSString *)identifier permissions:(CodePermission)permissions {
    CodeSegmentInfo *segmentInfo = _loadedSegments[identifier];
    if (!segmentInfo) {
        return NO;
    }
    
    int protFlags = 0;
    if (permissions & CodePermissionRead) protFlags |= PROT_READ;
    if (permissions & CodePermissionWrite) protFlags |= PROT_WRITE;
    if (permissions & CodePermissionExecute) protFlags |= PROT_EXEC;
    
    if (mprotect(segmentInfo.baseAddress, segmentInfo.size, protFlags) != 0) {
        NSLog(@"❌ 设置权限失败: %s", strerror(errno));
        return NO;
    }
    
    segmentInfo.permissions = permissions;
    NSLog(@"✅ 权限设置成功: %@", identifier);
    return YES;
}

- (NSDictionary *)validateCodeSecurity:(NSData *)machineCode {
    NSMutableDictionary *result = [[NSMutableDictionary alloc] init];
    result[@"safe"] = @YES;
    result[@"warnings"] = [[NSMutableArray alloc] init];
    
    // 检查代码长度
    if (machineCode.length == 0) {
        result[@"safe"] = @NO;
        result[@"reason"] = @"代码长度为0";
        return result;
    }
    
    if (machineCode.length > 1024 * 1024) { // 1MB限制
        result[@"safe"] = @NO;
        result[@"reason"] = @"代码长度超过限制";
        return result;
    }
    
    // 检查对齐
    if (machineCode.length % 2 != 0) {
        [result[@"warnings"] addObject:@"代码长度不是16位对齐"];
    }
    
    // 扫描危险指令
    NSArray *dangerousInsts = [self scanForDangerousInstructions:machineCode];
    if (dangerousInsts.count > 0) {
        result[@"safe"] = @NO;
        result[@"reason"] = [NSString stringWithFormat:@"包含危险指令: %@", [dangerousInsts componentsJoinedByString:@", "]];
        return result;
    }
    
    result[@"hash"] = [self calculateCodeHash:machineCode];
    return result;
}

- (BOOL)validateCodeIntegrity:(NSData *)machineCode expectedHash:(NSString *)expectedHash {
    NSString *actualHash = [self calculateCodeHash:machineCode];
    return [actualHash isEqualToString:expectedHash];
}

- (NSString *)calculateCodeHash:(NSData *)machineCode {
    unsigned char hash[CC_SHA256_DIGEST_LENGTH];
    CC_SHA256(machineCode.bytes, (CC_LONG)machineCode.length, hash);
    
    NSMutableString *hashString = [[NSMutableString alloc] init];
    for (int i = 0; i < CC_SHA256_DIGEST_LENGTH; i++) {
        [hashString appendFormat:@"%02x", hash[i]];
    }
    
    return [hashString copy];
}

- (NSArray<NSString *> *)scanForDangerousInstructions:(NSData *)machineCode {
    NSMutableArray *dangerous = [[NSMutableArray alloc] init];
    
    // 简化的危险指令检测
    const uint16_t *instructions = (const uint16_t *)machineCode.bytes;
    NSUInteger instructionCount = machineCode.length / sizeof(uint16_t);
    
    for (NSUInteger i = 0; i < instructionCount; i++) {
        uint16_t instruction = instructions[i];
        
        // 检查SVC指令 (0xDF00-0xDFFF)
        if ((instruction & 0xFF00) == 0xDF00) {
            [dangerous addObject:@"svc"];
        }
        
        // 检查其他危险指令模式
        // 这里可以添加更多的指令检测逻辑
    }
    
    return [dangerous copy];
}

- (NSArray<CodeSegmentInfo *> *)getAllLoadedSegments {
    return [_loadedSegments.allValues copy];
}

- (void)clearAllSegments {
    NSArray *identifiers = [_loadedSegments.allKeys copy];
    for (NSString *identifier in identifiers) {
        [self unloadCode:identifier];
    }
    
    NSLog(@"✅ 清理所有代码段完成");
}

- (NSDictionary *)getMemoryUsageStats {
    return @{
        @"totalAllocatedMemory": @(_totalAllocatedMemory),
        @"loadedSegmentCount": @(_loadedSegments.count),
        @"averageSegmentSize": _loadedSegments.count > 0 ? @(_totalAllocatedMemory / _loadedSegments.count) : @0
    };
}

- (BOOL)setCodeMetadata:(NSString *)identifier metadata:(NSDictionary *)metadata {
    CodeSegmentInfo *segmentInfo = _loadedSegments[identifier];
    if (!segmentInfo) {
        return NO;
    }
    
    NSMutableDictionary *newMetadata = [segmentInfo.metadata mutableCopy];
    [newMetadata addEntriesFromDictionary:metadata];
    segmentInfo.metadata = [newMetadata copy];
    
    return YES;
}

- (BOOL)setCodeSegmentEnabled:(NSString *)identifier enabled:(BOOL)enabled {
    CodeSegmentInfo *segmentInfo = _loadedSegments[identifier];
    if (!segmentInfo) {
        return NO;
    }
    
    segmentInfo.enabled = enabled;
    NSLog(@"✅ 代码段%@: %@", enabled ? @"启用" : @"禁用", identifier);
    return YES;
}

- (BOOL)isCodeSegmentExecutable:(NSString *)identifier {
    CodeSegmentInfo *segmentInfo = _loadedSegments[identifier];
    if (!segmentInfo || !segmentInfo.enabled) {
        return NO;
    }
    
    return (segmentInfo.permissions & CodePermissionExecute) != 0;
}

@end
