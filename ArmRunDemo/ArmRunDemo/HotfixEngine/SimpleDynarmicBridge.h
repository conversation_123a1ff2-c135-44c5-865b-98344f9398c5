//
//  SimpleDynarmicBridge.h
//  ArmRunDemo
//
//  Created by <PERSON> on 2025/7/4.
//  Copyright © 2025年 Ming Dai. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * 简化版的ARM代码执行引擎
 * 用于验证基础功能，不依赖完整的dynarmic
 */
@interface SimpleDynarmicBridge : NSObject

/**
 * 单例实例
 */
+ (instancetype)sharedInstance;

/**
 * 初始化引擎
 * @return 是否初始化成功
 */
- (BOOL)initializeEngine;

/**
 * 模拟执行ARM代码
 * @param armCode ARM机器码数据
 * @param params 参数数组
 * @param result 执行结果（输出参数）
 * @return 是否执行成功
 */
- (BOOL)executeARMCode:(NSData *)armCode 
            withParams:(NSArray *)params
                result:(id _Nullable * _Nullable)result;

/**
 * 获取模拟寄存器值
 * @param registerIndex 寄存器索引 (0-15)
 * @return 寄存器值
 */
- (uint32_t)getRegister:(NSUInteger)registerIndex;

/**
 * 设置模拟寄存器值
 * @param registerIndex 寄存器索引 (0-15)
 * @param value 寄存器值
 */
- (void)setRegister:(NSUInteger)registerIndex value:(uint32_t)value;

/**
 * 获取模拟CPSR状态寄存器
 * @return CPSR值
 */
- (uint32_t)getCPSR;

/**
 * 设置模拟CPSR状态寄存器
 * @param cpsr CPSR值
 */
- (void)setCPSR:(uint32_t)cpsr;

/**
 * 重置CPU状态
 */
- (void)reset;

/**
 * 清理缓存
 */
- (void)clearCache;

@end

NS_ASSUME_NONNULL_END
