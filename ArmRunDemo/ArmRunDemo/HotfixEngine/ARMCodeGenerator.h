//
//  ARMCodeGenerator.h
//  ArmRunDemo
//
//  Created by <PERSON> on 2025/7/4.
//  Copyright © 2025年 Ming Dai. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <objc/runtime.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * ARM指令类型
 */
typedef NS_ENUM(NSUInteger, ARMInstructionType) {
    ARMInstructionTypeMove,      // MOV指令
    ARMInstructionTypeAdd,       // ADD指令
    ARMInstructionTypeSubtract,  // SUB指令
    ARMInstructionTypeMultiply,  // MUL指令
    ARMInstructionTypeShift,     // 移位指令
    ARMInstructionTypeBranch,    // 分支指令
    ARMInstructionTypeLoad,      // 加载指令
    ARMInstructionTypeStore,     // 存储指令
    ARMInstructionTypeCall,      // 调用指令
    ARMInstructionTypeReturn     // 返回指令
};

/**
 * ARM寄存器
 */
typedef NS_ENUM(NSUInteger, ARMRegister) {
    ARMRegisterR0 = 0,
    ARMRegisterR1 = 1,
    ARMRegisterR2 = 2,
    ARMRegisterR3 = 3,
    ARMRegisterR4 = 4,
    ARMRegisterR5 = 5,
    ARMRegisterR6 = 6,
    ARMRegisterR7 = 7,
    ARMRegisterR8 = 8,
    ARMRegisterR9 = 9,
    ARMRegisterR10 = 10,
    ARMRegisterR11 = 11,
    ARMRegisterR12 = 12,
    ARMRegisterSP = 13,  // 栈指针
    ARMRegisterLR = 14,  // 链接寄存器
    ARMRegisterPC = 15   // 程序计数器
};

/**
 * ARM代码生成器
 * 将Objective-C方法调用转换为ARM汇编代码
 */
@interface ARMCodeGenerator : NSObject

/**
 * 单例实例
 */
+ (instancetype)sharedInstance;

/**
 * 为Objective-C方法生成ARM包装代码
 * @param targetClass 目标类
 * @param selector 目标方法选择器
 * @param isInstanceMethod 是否为实例方法
 * @return ARM机器码数据
 */
- (NSData * _Nullable)generateWrapperCodeForClass:(Class)targetClass
                                         selector:(SEL)selector
                                 isInstanceMethod:(BOOL)isInstanceMethod;

/**
 * 生成方法调用的ARM代码
 * @param methodSignature 方法签名
 * @param targetAddress 目标方法地址
 * @return ARM机器码数据
 */
- (NSData * _Nullable)generateMethodCallCode:(NSMethodSignature *)methodSignature
                               targetAddress:(uintptr_t)targetAddress;

/**
 * 生成参数传递代码
 * @param argumentTypes 参数类型数组
 * @return ARM机器码数据
 */
- (NSData * _Nullable)generateArgumentPassingCode:(NSArray<NSString *> *)argumentTypes;

/**
 * 生成返回值处理代码
 * @param returnType 返回值类型
 * @return ARM机器码数据
 */
- (NSData * _Nullable)generateReturnValueCode:(NSString *)returnType;

/**
 * 生成ARM指令
 * @param type 指令类型
 * @param operands 操作数数组
 * @return ARM机器码
 */
- (uint32_t)generateInstruction:(ARMInstructionType)type operands:(NSArray *)operands;

/**
 * 生成MOV指令
 * @param destReg 目标寄存器
 * @param value 立即数值
 * @return ARM机器码
 */
- (uint32_t)generateMOVInstruction:(ARMRegister)destReg value:(uint32_t)value;

/**
 * 生成ADD指令
 * @param destReg 目标寄存器
 * @param srcReg1 源寄存器1
 * @param srcReg2 源寄存器2
 * @return ARM机器码
 */
- (uint32_t)generateADDInstruction:(ARMRegister)destReg
                           srcReg1:(ARMRegister)srcReg1
                           srcReg2:(ARMRegister)srcReg2;

/**
 * 生成BL指令（带链接的分支）
 * @param targetAddress 目标地址
 * @param currentAddress 当前地址
 * @return ARM机器码
 */
- (uint32_t)generateBLInstruction:(uint32_t)targetAddress currentAddress:(uint32_t)currentAddress;

/**
 * 生成BX指令（寄存器分支）
 * @param reg 目标寄存器
 * @return ARM机器码
 */
- (uint32_t)generateBXInstruction:(ARMRegister)reg;

/**
 * 生成PUSH指令
 * @param registers 要压栈的寄存器列表
 * @return ARM机器码
 */
- (uint32_t)generatePUSHInstruction:(NSArray<NSNumber *> *)registers;

/**
 * 生成POP指令
 * @param registers 要出栈的寄存器列表
 * @return ARM机器码
 */
- (uint32_t)generatePOPInstruction:(NSArray<NSNumber *> *)registers;

/**
 * 将汇编代码转换为机器码
 * @param assembly 汇编代码字符串
 * @return ARM机器码数据
 */
- (NSData * _Nullable)assembleCode:(NSString *)assembly;

/**
 * 反汇编机器码
 * @param machineCode ARM机器码数据
 * @return 汇编代码字符串
 */
- (NSString * _Nullable)disassembleCode:(NSData *)machineCode;

/**
 * 验证ARM代码的有效性
 * @param machineCode ARM机器码数据
 * @return 是否有效
 */
- (BOOL)validateARMCode:(NSData *)machineCode;

/**
 * 获取方法调用约定信息
 * @param methodSignature 方法签名
 * @return 调用约定信息字典
 */
- (NSDictionary *)getCallingConvention:(NSMethodSignature *)methodSignature;

@end

NS_ASSUME_NONNULL_END
