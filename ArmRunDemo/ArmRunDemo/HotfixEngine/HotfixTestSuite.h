//
//  HotfixTestSuite.h
//  ArmRunDemo
//
//  Created by <PERSON> on 2025/7/4.
//  Copyright © 2025年 Ming Dai. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * 测试结果
 */
@interface HotfixTestResult : NSObject
@property (nonatomic, strong) NSString *testName;
@property (nonatomic, assign) BOOL passed;
@property (nonatomic, strong) NSString *message;
@property (nonatomic, assign) NSTimeInterval duration;
@property (nonatomic, strong) NSDictionary *details;
@end

/**
 * 热修复测试套件
 * 提供全面的功能测试和性能测试
 */
@interface HotfixTestSuite : NSObject

/**
 * 单例实例
 */
+ (instancetype)sharedInstance;

/**
 * 运行所有测试
 * @return 测试结果数组
 */
- (NSArray<HotfixTestResult *> *)runAllTests;

/**
 * 运行基础功能测试
 * @return 测试结果数组
 */
- (NSArray<HotfixTestResult *> *)runBasicTests;

/**
 * 运行ARM代码执行测试
 * @return 测试结果数组
 */
- (NSArray<HotfixTestResult *> *)runARMExecutionTests;

/**
 * 运行方法拦截测试
 * @return 测试结果数组
 */
- (NSArray<HotfixTestResult *> *)runMethodInterceptionTests;

/**
 * 运行代码加载测试
 * @return 测试结果数组
 */
- (NSArray<HotfixTestResult *> *)runCodeLoadingTests;

/**
 * 运行热修复管理测试
 * @return 测试结果数组
 */
- (NSArray<HotfixTestResult *> *)runHotfixManagementTests;

/**
 * 运行性能测试
 * @return 测试结果数组
 */
- (NSArray<HotfixTestResult *> *)runPerformanceTests;

/**
 * 运行异常处理测试
 * @return 测试结果数组
 */
- (NSArray<HotfixTestResult *> *)runExceptionHandlingTests;

/**
 * 生成测试报告
 * @param results 测试结果数组
 * @return 测试报告字符串
 */
- (NSString *)generateTestReport:(NSArray<HotfixTestResult *> *)results;

/**
 * 获取测试统计信息
 * @param results 测试结果数组
 * @return 统计信息字典
 */
- (NSDictionary *)getTestStatistics:(NSArray<HotfixTestResult *> *)results;

/**
 * 设置测试回调
 * @param callback 测试进度回调
 */
- (void)setTestProgressCallback:(void (^)(NSString *testName, BOOL completed))callback;

@end

NS_ASSUME_NONNULL_END
