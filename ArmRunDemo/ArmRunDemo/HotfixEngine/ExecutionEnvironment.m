//
//  ExecutionEnvironment.m
//  ArmRunDemo
//
//  Created by <PERSON> on 2025/7/4.
//  Copyright © 2025年 Ming Dai. All rights reserved.
//

#import "ExecutionEnvironment.h"

/**
 * 内存区域信息
 */
@interface MemoryRegion : NSObject
@property (nonatomic, assign) uint32_t baseAddress;
@property (nonatomic, assign) size_t size;
@property (nonatomic, strong) NSMutableData *data;
@property (nonatomic, assign) BOOL readable;
@property (nonatomic, assign) BOOL writable;
@property (nonatomic, assign) BOOL executable;
@end

@implementation MemoryRegion
@end

/**
 * CPU上下文信息
 */
@interface CPUContext : NSObject
@property (nonatomic, assign) CPUState state;
@property (nonatomic, strong) NSString *identifier;
@property (nonatomic, strong) NSDate *createTime;
@property (nonatomic, assign) BOOL active;
@end

@implementation CPUContext
@end

@interface ExecutionEnvironment ()
@property (nonatomic, assign) CPUState currentState;
@property (nonatomic, strong) NSMutableDictionary<NSString *, CPUContext *> *contexts;
@property (nonatomic, strong) NSMutableDictionary<NSString *, CPUContext *> *savedStates;
@property (nonatomic, strong) NSMutableArray<MemoryRegion *> *memoryRegions;
@property (nonatomic, strong) NSString *currentContextId;
@property (nonatomic, assign) uint32_t nextMemoryAddress;
@property (nonatomic, assign) BOOL debugMode;
@property (nonatomic, assign) uint64_t instructionCount;
@property (nonatomic, assign) uint64_t memoryAccessCount;

// 回调
@property (nonatomic, copy) MemoryReadCallback memoryReadCallback;
@property (nonatomic, copy) MemoryWriteCallback memoryWriteCallback;
@property (nonatomic, copy) SystemCallCallback systemCallCallback;
@property (nonatomic, copy) ExceptionCallback exceptionCallback;
@end

@implementation ExecutionEnvironment

+ (instancetype)sharedInstance {
    static ExecutionEnvironment *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[ExecutionEnvironment alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _contexts = [[NSMutableDictionary alloc] init];
        _savedStates = [[NSMutableDictionary alloc] init];
        _memoryRegions = [[NSMutableArray alloc] init];
        _nextMemoryAddress = 0x10000000; // 起始地址256MB
        _debugMode = NO;
        _instructionCount = 0;
        _memoryAccessCount = 0;
        [self initializeDefaultState];
    }
    return self;
}

- (void)initializeDefaultState {
    // 初始化默认CPU状态
    memset(&_currentState, 0, sizeof(CPUState));
    _currentState.cpsr = 0x00000010; // ARM模式，用户模式
    _currentState.registers[13] = 0x20000000; // 栈指针
    _currentState.registers[15] = 0x00000000; // 程序计数器
}

- (BOOL)initialize {
    NSLog(@"🔧 初始化执行环境...");
    
    // 创建默认上下文
    BOOL success = [self createContext:@"default"];
    if (success) {
        _currentContextId = @"default";
        NSLog(@"✅ 执行环境初始化成功");
    } else {
        NSLog(@"❌ 执行环境初始化失败");
    }
    
    return success;
}

- (BOOL)createContext:(NSString *)identifier {
    if (_contexts[identifier]) {
        NSLog(@"⚠️ 上下文已存在: %@", identifier);
        return NO;
    }
    
    CPUContext *context = [[CPUContext alloc] init];
    context.identifier = identifier;
    context.state = _currentState;
    context.createTime = [NSDate date];
    context.active = NO;
    
    _contexts[identifier] = context;
    
    NSLog(@"✅ 创建上下文: %@", identifier);
    return YES;
}

- (BOOL)switchToContext:(NSString *)identifier {
    CPUContext *context = _contexts[identifier];
    if (!context) {
        NSLog(@"❌ 上下文不存在: %@", identifier);
        return NO;
    }
    
    // 保存当前状态
    if (_currentContextId) {
        CPUContext *currentContext = _contexts[_currentContextId];
        if (currentContext) {
            currentContext.state = _currentState;
            currentContext.active = NO;
        }
    }
    
    // 切换到新上下文
    _currentState = context.state;
    _currentContextId = identifier;
    context.active = YES;
    
    NSLog(@"🔄 切换到上下文: %@", identifier);
    return YES;
}

- (NSString *)getCurrentContextIdentifier {
    return _currentContextId;
}

- (BOOL)saveCPUState:(NSString *)identifier {
    CPUContext *savedContext = [[CPUContext alloc] init];
    savedContext.identifier = identifier;
    savedContext.state = _currentState;
    savedContext.createTime = [NSDate date];
    
    _savedStates[identifier] = savedContext;
    
    NSLog(@"💾 保存CPU状态: %@", identifier);
    return YES;
}

- (BOOL)restoreCPUState:(NSString *)identifier {
    CPUContext *savedContext = _savedStates[identifier];
    if (!savedContext) {
        NSLog(@"❌ 保存的状态不存在: %@", identifier);
        return NO;
    }
    
    _currentState = savedContext.state;
    
    NSLog(@"📥 恢复CPU状态: %@", identifier);
    return YES;
}

- (CPUState)getCPUState {
    return _currentState;
}

- (void)setCPUState:(CPUState)state {
    _currentState = state;
    if (_debugMode) {
        NSLog(@"🔧 设置CPU状态");
    }
}

- (uint32_t)getRegister:(NSUInteger)registerIndex {
    if (registerIndex >= 16) {
        NSLog(@"❌ 寄存器索引超出范围: %lu", (unsigned long)registerIndex);
        return 0;
    }
    
    return _currentState.registers[registerIndex];
}

- (void)setRegister:(NSUInteger)registerIndex value:(uint32_t)value {
    if (registerIndex >= 16) {
        NSLog(@"❌ 寄存器索引超出范围: %lu", (unsigned long)registerIndex);
        return;
    }
    
    _currentState.registers[registerIndex] = value;
    
    if (_debugMode) {
        NSLog(@"🔧 设置寄存器 r%lu = 0x%08X", (unsigned long)registerIndex, value);
    }
}

- (uint32_t)getCPSR {
    return _currentState.cpsr;
}

- (void)setCPSR:(uint32_t)cpsr {
    _currentState.cpsr = cpsr;
    
    if (_debugMode) {
        NSLog(@"🔧 设置CPSR = 0x%08X", cpsr);
    }
}

- (ExecutionMode)getCurrentMode {
    uint32_t mode = _currentState.cpsr & 0x1F;
    switch (mode) {
        case 0x10: return ExecutionModeUser;
        case 0x13: return ExecutionModeSupervisor;
        case 0x1F: return ExecutionModeSystem;
        case 0x17: return ExecutionModeAbort;
        case 0x1B: return ExecutionModeUndefined;
        case 0x12: return ExecutionModeIRQ;
        case 0x11: return ExecutionModeFIQ;
        default: return ExecutionModeUser;
    }
}

- (void)setExecutionMode:(ExecutionMode)mode {
    uint32_t cpsr = _currentState.cpsr & ~0x1F;
    
    switch (mode) {
        case ExecutionModeUser:       cpsr |= 0x10; break;
        case ExecutionModeSupervisor: cpsr |= 0x13; break;
        case ExecutionModeSystem:     cpsr |= 0x1F; break;
        case ExecutionModeAbort:      cpsr |= 0x17; break;
        case ExecutionModeUndefined:  cpsr |= 0x1B; break;
        case ExecutionModeIRQ:        cpsr |= 0x12; break;
        case ExecutionModeFIQ:        cpsr |= 0x11; break;
    }
    
    _currentState.cpsr = cpsr;
}

- (uint64_t)readMemory:(uint32_t)address size:(NSUInteger)size {
    _memoryAccessCount++;
    
    if (_debugMode) {
        NSLog(@"📖 读取内存: 0x%08X, 大小: %lu", address, (unsigned long)size);
    }
    
    // 查找内存区域
    MemoryRegion *region = [self findMemoryRegion:address];
    if (!region) {
        if (_memoryReadCallback) {
            return _memoryReadCallback(address, size);
        }
        NSLog(@"❌ 内存地址无效: 0x%08X", address);
        return 0;
    }
    
    if (!region.readable) {
        [self triggerException:@"MemoryAccessViolation" address:address];
        return 0;
    }
    
    uint32_t offset = address - region.baseAddress;
    if (offset + size > region.size) {
        NSLog(@"❌ 内存访问越界: 0x%08X", address);
        return 0;
    }
    
    const uint8_t *data = region.data.bytes;
    uint64_t value = 0;
    
    switch (size) {
        case 1:
            value = data[offset];
            break;
        case 2:
            value = *(uint16_t *)(data + offset);
            break;
        case 4:
            value = *(uint32_t *)(data + offset);
            break;
        case 8:
            value = *(uint64_t *)(data + offset);
            break;
        default:
            NSLog(@"❌ 不支持的内存访问大小: %lu", (unsigned long)size);
            return 0;
    }
    
    return value;
}

- (BOOL)writeMemory:(uint32_t)address value:(uint64_t)value size:(NSUInteger)size {
    _memoryAccessCount++;
    
    if (_debugMode) {
        NSLog(@"📝 写入内存: 0x%08X = 0x%llX, 大小: %lu", address, value, (unsigned long)size);
    }
    
    // 查找内存区域
    MemoryRegion *region = [self findMemoryRegion:address];
    if (!region) {
        if (_memoryWriteCallback) {
            _memoryWriteCallback(address, (uint32_t)value, size);
            return YES;
        }
        NSLog(@"❌ 内存地址无效: 0x%08X", address);
        return NO;
    }
    
    if (!region.writable) {
        [self triggerException:@"MemoryAccessViolation" address:address];
        return NO;
    }
    
    uint32_t offset = address - region.baseAddress;
    if (offset + size > region.size) {
        NSLog(@"❌ 内存访问越界: 0x%08X", address);
        return NO;
    }
    
    uint8_t *data = region.data.mutableBytes;
    
    switch (size) {
        case 1:
            data[offset] = (uint8_t)value;
            break;
        case 2:
            *(uint16_t *)(data + offset) = (uint16_t)value;
            break;
        case 4:
            *(uint32_t *)(data + offset) = (uint32_t)value;
            break;
        case 8:
            *(uint64_t *)(data + offset) = value;
            break;
        default:
            NSLog(@"❌ 不支持的内存访问大小: %lu", (unsigned long)size);
            return NO;
    }
    
    return YES;
}

- (MemoryRegion *)findMemoryRegion:(uint32_t)address {
    for (MemoryRegion *region in _memoryRegions) {
        if (address >= region.baseAddress && 
            address < region.baseAddress + region.size) {
            return region;
        }
    }
    return nil;
}

- (uint32_t)allocateMemory:(size_t)size baseAddress:(uint32_t)baseAddress {
    if (baseAddress == 0) {
        baseAddress = _nextMemoryAddress;
        _nextMemoryAddress += (uint32_t)((size + 4095) & ~4095); // 4KB对齐
    }
    
    // 检查地址冲突
    for (MemoryRegion *region in _memoryRegions) {
        if (baseAddress < region.baseAddress + region.size &&
            baseAddress + size > region.baseAddress) {
            NSLog(@"❌ 内存地址冲突: 0x%08X", baseAddress);
            return 0;
        }
    }
    
    MemoryRegion *region = [[MemoryRegion alloc] init];
    region.baseAddress = baseAddress;
    region.size = size;
    region.data = [[NSMutableData alloc] initWithLength:size];
    region.readable = YES;
    region.writable = YES;
    region.executable = NO;
    
    [_memoryRegions addObject:region];
    
    NSLog(@"✅ 分配内存: 0x%08X, 大小: %lu", baseAddress, (unsigned long)size);
    return baseAddress;
}

- (BOOL)deallocateMemory:(uint32_t)baseAddress size:(size_t)size {
    for (NSInteger i = _memoryRegions.count - 1; i >= 0; i--) {
        MemoryRegion *region = _memoryRegions[i];
        if (region.baseAddress == baseAddress && region.size == size) {
            [_memoryRegions removeObjectAtIndex:i];
            NSLog(@"✅ 释放内存: 0x%08X", baseAddress);
            return YES;
        }
    }
    
    NSLog(@"❌ 内存区域不存在: 0x%08X", baseAddress);
    return NO;
}

- (void)setMemoryCallbacks:(MemoryReadCallback)readCallback
             writeCallback:(MemoryWriteCallback)writeCallback {
    _memoryReadCallback = readCallback;
    _memoryWriteCallback = writeCallback;
}

- (void)setSystemCallCallback:(SystemCallCallback)callback {
    _systemCallCallback = callback;
}

- (void)setExceptionCallback:(ExceptionCallback)callback {
    _exceptionCallback = callback;
}

- (void)triggerSystemCall:(uint32_t)callNumber {
    if (_systemCallCallback) {
        _systemCallCallback(callNumber, &_currentState);
    } else {
        NSLog(@"⚠️ 系统调用未处理: %u", callNumber);
    }
}

- (void)triggerException:(NSString *)exceptionType address:(uint32_t)address {
    if (_exceptionCallback) {
        _exceptionCallback(exceptionType, address, &_currentState);
    } else {
        NSLog(@"⚠️ 异常未处理: %@ at 0x%08X", exceptionType, address);
    }
}

- (void)reset {
    [self initializeDefaultState];
    [_contexts removeAllObjects];
    [_savedStates removeAllObjects];
    [_memoryRegions removeAllObjects];
    _currentContextId = nil;
    _nextMemoryAddress = 0x10000000;
    _instructionCount = 0;
    _memoryAccessCount = 0;
    
    NSLog(@"🔄 执行环境已重置");
}

- (NSDictionary *)getExecutionStats {
    return @{
        @"instructionCount": @(_instructionCount),
        @"memoryAccessCount": @(_memoryAccessCount),
        @"contextCount": @(_contexts.count),
        @"memoryRegionCount": @(_memoryRegions.count),
        @"currentMode": @([self getCurrentMode]),
        @"debugMode": @(_debugMode)
    };
}

- (void)setDebugMode:(BOOL)enabled {
    _debugMode = enabled;
    NSLog(@"🐛 调试模式: %@", enabled ? @"启用" : @"禁用");
}

- (NSArray<NSDictionary *> *)getMemoryMap {
    NSMutableArray *memoryMap = [[NSMutableArray alloc] init];
    
    for (MemoryRegion *region in _memoryRegions) {
        [memoryMap addObject:@{
            @"baseAddress": [NSString stringWithFormat:@"0x%08X", region.baseAddress],
            @"size": @(region.size),
            @"readable": @(region.readable),
            @"writable": @(region.writable),
            @"executable": @(region.executable)
        }];
    }
    
    return [memoryMap copy];
}

@end
