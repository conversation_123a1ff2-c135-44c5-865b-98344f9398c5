//
//  DynarmicBridge.h
//  ArmRunDemo
//
//  Created by <PERSON> on 2025/7/4.
//  Copyright © 2025年 Ming Dai. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * Dynarmic ARM JIT引擎的Objective-C桥接类
 * 提供ARM代码动态编译和执行功能
 */
@interface DynarmicBridge : NSObject

/**
 * 单例实例
 */
+ (instancetype)sharedInstance;

/**
 * 初始化Dynarmic JIT引擎
 * @return 是否初始化成功
 */
- (BOOL)initializeEngine;

/**
 * 执行ARM机器码
 * @param armCode ARM机器码数据
 * @param params 参数数组
 * @param result 执行结果（输出参数）
 * @return 是否执行成功
 */
- (BOOL)executeARMCode:(NSData *)armCode 
            withParams:(NSArray *)params
                result:(id _Nullable * _Nullable)result;

/**
 * 执行ARM汇编指令
 * @param assembly ARM汇编字符串
 * @param params 参数数组
 * @param result 执行结果（输出参数）
 * @return 是否执行成功
 */
- (BOOL)executeAssembly:(NSString *)assembly
             withParams:(NSArray *)params
                 result:(id _Nullable * _Nullable)result;

/**
 * 清理代码缓存
 */
- (void)clearCache;

/**
 * 重置CPU状态
 */
- (void)reset;

/**
 * 获取CPU寄存器值
 * @param registerIndex 寄存器索引 (0-15)
 * @return 寄存器值
 */
- (uint32_t)getRegister:(NSUInteger)registerIndex;

/**
 * 设置CPU寄存器值
 * @param registerIndex 寄存器索引 (0-15)
 * @param value 寄存器值
 */
- (void)setRegister:(NSUInteger)registerIndex value:(uint32_t)value;

/**
 * 获取CPSR状态寄存器
 * @return CPSR值
 */
- (uint32_t)getCPSR;

/**
 * 设置CPSR状态寄存器
 * @param cpsr CPSR值
 */
- (void)setCPSR:(uint32_t)cpsr;

/**
 * 检查引擎是否正在执行
 * @return 是否正在执行
 */
- (BOOL)isExecuting;

@end

NS_ASSUME_NONNULL_END
