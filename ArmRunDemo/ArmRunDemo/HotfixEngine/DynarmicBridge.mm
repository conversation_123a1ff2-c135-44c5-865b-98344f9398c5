//
//  DynarmicBridge.mm
//  ArmRunDemo
//
//  Created by <PERSON> on 2025/7/4.
//  Copyright © 2025年 Ming Dai. All rights reserved.
//

#import "DynarmicBridge.h"

// 包含必要的C++头文件
#include <array>
#include <cstdint>
#include <memory>
#include <vector>

// 包含dynarmic头文件
#include "../Dynarmic/Interface/a32.h"
#include "../Dynarmic/Interface/config.h"

using u8 = std::uint8_t;
using u16 = std::uint16_t;
using u32 = std::uint32_t;
using u64 = std::uint64_t;

/**
 * Dynarmic环境回调类
 * 实现内存访问和系统调用接口
 */
class DynarmicEnvironment final : public Dynarmic::A32::UserCallbacks {
public:
    u64 ticks_left = 0;
    std::array<u8, 1024 * 1024> memory{}; // 1MB内存空间
    
    u8 MemoryRead8(u32 vaddr) override {
        if (vaddr >= memory.size()) {
            return 0;
        }
        return memory[vaddr];
    }
    
    u16 MemoryRead16(u32 vaddr) override {
        return u16(MemoryRead8(vaddr)) | u16(MemoryRead8(vaddr + 1)) << 8;
    }
    
    u32 MemoryRead32(u32 vaddr) override {
        return u32(MemoryRead16(vaddr)) | u32(MemoryRead16(vaddr + 2)) << 16;
    }
    
    u64 MemoryRead64(u32 vaddr) override {
        return u64(MemoryRead32(vaddr)) | u64(MemoryRead32(vaddr + 4)) << 32;
    }
    
    void MemoryWrite8(u32 vaddr, u8 value) override {
        if (vaddr >= memory.size()) {
            return;
        }
        memory[vaddr] = value;
    }
    
    void MemoryWrite16(u32 vaddr, u16 value) override {
        MemoryWrite8(vaddr, u8(value));
        MemoryWrite8(vaddr + 1, u8(value >> 8));
    }
    
    void MemoryWrite32(u32 vaddr, u32 value) override {
        MemoryWrite16(vaddr, u16(value));
        MemoryWrite16(vaddr + 2, u16(value >> 16));
    }
    
    void MemoryWrite64(u32 vaddr, u64 value) override {
        MemoryWrite32(vaddr, u32(value));
        MemoryWrite32(vaddr + 4, u32(value >> 32));
    }
    
    void InterpreterFallback(u32 pc, size_t num_instructions) override {
        // 解释器回退 - 暂时不实现
    }
    
    void CallSVC(u32 swi) override {
        // 系统调用 - 暂时不实现
    }
    
    void ExceptionRaised(u32 pc, Dynarmic::A32::Exception exception) override {
        // 异常处理 - 暂时不实现
    }
    
    void AddTicks(u64 ticks) override {
        if (ticks > ticks_left) {
            ticks_left = 0;
            return;
        }
        ticks_left -= ticks;
    }
    
    u64 GetTicksRemaining() override {
        return ticks_left;
    }
    
    // 写入ARM代码到内存
    void WriteCode(u32 address, const std::vector<u8>& code) {
        for (size_t i = 0; i < code.size() && (address + i) < memory.size(); ++i) {
            memory[address + i] = code[i];
        }
    }
};

@interface DynarmicBridge ()
@property (nonatomic, assign) std::unique_ptr<DynarmicEnvironment> environment;
@property (nonatomic, assign) std::unique_ptr<Dynarmic::A32::Jit> jit;
@property (nonatomic, assign) BOOL initialized;
@end

@implementation DynarmicBridge

+ (instancetype)sharedInstance {
    static DynarmicBridge *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[DynarmicBridge alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _initialized = NO;
    }
    return self;
}

- (BOOL)initializeEngine {
    if (_initialized) {
        return YES;
    }
    
    @try {
        // 创建环境
        _environment = std::make_unique<DynarmicEnvironment>();
        
        // 配置JIT
        Dynarmic::A32::UserConfig config;
        config.callbacks = _environment.get();
        
        // 创建JIT实例
        _jit = std::make_unique<Dynarmic::A32::Jit>(config);
        
        _initialized = YES;
        return YES;
    }
    @catch (NSException *exception) {
        NSLog(@"Failed to initialize Dynarmic engine: %@", exception.reason);
        return NO;
    }
}

- (BOOL)executeARMCode:(NSData *)armCode 
            withParams:(NSArray *)params
                result:(id *)result {
    if (!_initialized || !_jit) {
        return NO;
    }
    
    @try {
        // 将ARM代码写入内存
        std::vector<u8> code((u8*)armCode.bytes, (u8*)armCode.bytes + armCode.length);
        _environment->WriteCode(0x1000, code);
        
        // 设置参数到寄存器
        for (NSUInteger i = 0; i < MIN(params.count, 4); i++) {
            NSNumber *param = params[i];
            _jit->Regs()[i] = param.unsignedIntValue;
        }
        
        // 设置PC和执行模式
        _jit->Regs()[15] = 0x1000; // PC = 0x1000
        _jit->SetCpsr(0x00000010); // ARM模式
        
        // 设置执行时间
        _environment->ticks_left = 1000;
        
        // 执行代码
        Dynarmic::HaltReason halt_reason = _jit->Run();
        
        // 获取返回值
        if (result) {
            *result = @(_jit->Regs()[0]);
        }
        
        return halt_reason == Dynarmic::HaltReason::UserDefined1 || 
               halt_reason == Dynarmic::HaltReason::Step;
    }
    @catch (NSException *exception) {
        NSLog(@"Failed to execute ARM code: %@", exception.reason);
        return NO;
    }
}

- (BOOL)executeAssembly:(NSString *)assembly
             withParams:(NSArray *)params
                 result:(id *)result {
    // TODO: 实现汇编器功能
    // 暂时返回NO，后续可以集成汇编器
    return NO;
}

- (void)clearCache {
    if (_jit) {
        _jit->ClearCache();
    }
}

- (void)reset {
    if (_jit) {
        _jit->Reset();
    }
}

- (uint32_t)getRegister:(NSUInteger)registerIndex {
    if (_jit && registerIndex < 16) {
        return _jit->Regs()[registerIndex];
    }
    return 0;
}

- (void)setRegister:(NSUInteger)registerIndex value:(uint32_t)value {
    if (_jit && registerIndex < 16) {
        _jit->Regs()[registerIndex] = value;
    }
}

- (uint32_t)getCPSR {
    if (_jit) {
        return _jit->Cpsr();
    }
    return 0;
}

- (void)setCPSR:(uint32_t)cpsr {
    if (_jit) {
        _jit->SetCpsr(cpsr);
    }
}

- (BOOL)isExecuting {
    if (_jit) {
        return _jit->IsExecuting();
    }
    return NO;
}

@end
