//
//  DynarmicBridge.mm
//  ArmRunDemo
//
//  Created by <PERSON> on 2025/7/4.
//  Copyright © 2025年 Ming Dai. All rights reserved.
//

#import "DynarmicBridge.h"
#import "SimpleDynarmicBridge.h"

@interface DynarmicBridge ()
@property (nonatomic, strong) SimpleDynarmicBridge *simpleBridge;
@property (nonatomic, assign) BOOL initialized;
@end

@implementation DynarmicBridge

+ (instancetype)sharedInstance {
    static DynarmicBridge *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[DynarmicBridge alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _initialized = NO;
    }
    return self;
}

- (BOOL)initializeEngine {
    if (_initialized) {
        return YES;
    }

    _simpleBridge = [SimpleDynarmicBridge sharedInstance];
    BOOL success = [_simpleBridge initializeEngine];

    if (success) {
        _initialized = YES;
    }

    return success;
}

- (BOOL)executeARMCode:(NSData *)armCode
            withParams:(NSArray *)params
                result:(id *)result {
    if (!_initialized || !_simpleBridge) {
        return NO;
    }

    return [_simpleBridge executeARMCode:armCode withParams:params result:result];
}

- (BOOL)executeAssembly:(NSString *)assembly
             withParams:(NSArray *)params
                 result:(id *)result {
    // TODO: 实现汇编器功能
    // 暂时返回NO，后续可以集成汇编器
    return NO;
}

- (void)clearCache {
    [_simpleBridge clearCache];
}

- (void)reset {
    [_simpleBridge reset];
}

- (uint32_t)getRegister:(NSUInteger)registerIndex {
    return [_simpleBridge getRegister:registerIndex];
}

- (void)setRegister:(NSUInteger)registerIndex value:(uint32_t)value {
    [_simpleBridge setRegister:registerIndex value:value];
}

- (uint32_t)getCPSR {
    return [_simpleBridge getCPSR];
}

- (void)setCPSR:(uint32_t)cpsr {
    [_simpleBridge setCPSR:cpsr];
}

- (BOOL)isExecuting {
    // SimpleDynarmicBridge doesn't have this method, return NO
    return NO;
}

@end
