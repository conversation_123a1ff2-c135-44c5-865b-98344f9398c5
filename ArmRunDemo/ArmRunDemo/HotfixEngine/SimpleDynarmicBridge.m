//
//  SimpleDynarmicBridge.m
//  ArmRunDemo
//
//  Created by <PERSON> on 2025/7/4.
//  Copyright © 2025年 Ming Dai. All rights reserved.
//

#import "SimpleDynarmicBridge.h"

@interface SimpleDynarmicBridge ()
@property (nonatomic, assign) uint32_t registers[16];
@property (nonatomic, assign) uint32_t cpsr;
@property (nonatomic, assign) BOOL initialized;
@property (nonatomic, strong) NSMutableData *memory;
@end

@implementation SimpleDynarmicBridge

+ (instancetype)sharedInstance {
    static SimpleDynarmicBridge *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[SimpleDynarmicBridge alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _initialized = NO;
        _memory = [[NSMutableData alloc] initWithLength:1024 * 1024]; // 1MB内存
        [self reset];
    }
    return self;
}

- (BOOL)initializeEngine {
    if (_initialized) {
        return YES;
    }
    
    NSLog(@"初始化简化版ARM执行引擎...");
    _initialized = YES;
    return YES;
}

- (BOOL)executeARMCode:(NSData *)armCode 
            withParams:(NSArray *)params
                result:(id *)result {
    if (!_initialized) {
        NSLog(@"引擎未初始化");
        return NO;
    }
    
    NSLog(@"开始执行ARM代码模拟...");
    
    // 设置参数到寄存器
    for (NSUInteger i = 0; i < MIN(params.count, 4); i++) {
        NSNumber *param = params[i];
        _registers[i] = param.unsignedIntValue;
        NSLog(@"设置 r%lu = %u", (unsigned long)i, _registers[i]);
    }
    
    // 模拟ARM指令执行
    if (armCode.length >= 2) {
        const uint16_t *instructions = (const uint16_t *)armCode.bytes;
        uint16_t firstInstruction = instructions[0];
        
        NSLog(@"执行指令: 0x%04X", firstInstruction);
        
        // 模拟 lsls r0, r1, #2 指令 (0x0088)
        if (firstInstruction == 0x0088) {
            NSLog(@"识别指令: lsls r0, r1, #2");
            uint32_t r1_value = _registers[1];
            _registers[0] = r1_value << 2;
            NSLog(@"执行: r0 = r1 << 2 = %u << 2 = %u", r1_value, _registers[0]);
            
            // 设置标志位
            if (_registers[0] == 0) {
                _cpsr |= (1 << 30); // Z flag
            } else {
                _cpsr &= ~(1 << 30);
            }
            
            if (_registers[0] & (1 << 31)) {
                _cpsr |= (1 << 31); // N flag
            } else {
                _cpsr &= ~(1 << 31);
            }
        }
        // 模拟 add r0, r1, r2 指令 (0x1888)
        else if (firstInstruction == 0x1888) {
            NSLog(@"识别指令: add r0, r1, r2");
            _registers[0] = _registers[1] + _registers[2];
            NSLog(@"执行: r0 = r1 + r2 = %u + %u = %u", _registers[1], _registers[2], _registers[0]);
        }
        // 模拟 mov r0, #imm 指令 (0x20XX)
        else if ((firstInstruction & 0xF800) == 0x2000) {
            uint8_t rd = (firstInstruction >> 8) & 0x7;
            uint8_t imm = firstInstruction & 0xFF;
            _registers[rd] = imm;
            NSLog(@"识别指令: mov r%u, #%u", rd, imm);
            NSLog(@"执行: r%u = %u", rd, imm);
        }
        // 其他指令
        else {
            NSLog(@"未识别的指令，执行默认操作");
            // 默认操作：将r1的值复制到r0
            _registers[0] = _registers[1];
        }
    }
    
    // 返回结果
    if (result) {
        *result = @(_registers[0]);
    }
    
    NSLog(@"ARM代码执行完成，结果: %u", _registers[0]);
    return YES;
}

- (uint32_t)getRegister:(NSUInteger)registerIndex {
    if (registerIndex < 16) {
        return _registers[registerIndex];
    }
    return 0;
}

- (void)setRegister:(NSUInteger)registerIndex value:(uint32_t)value {
    if (registerIndex < 16) {
        _registers[registerIndex] = value;
    }
}

- (uint32_t)getCPSR {
    return _cpsr;
}

- (void)setCPSR:(uint32_t)cpsr {
    _cpsr = cpsr;
}

- (void)reset {
    // 清零所有寄存器
    for (int i = 0; i < 16; i++) {
        _registers[i] = 0;
    }
    
    // 设置默认CPSR值 (ARM模式)
    _cpsr = 0x00000010;
    
    // 清零内存
    [_memory resetBytesInRange:NSMakeRange(0, _memory.length)];
    
    NSLog(@"CPU状态已重置");
}

- (void)clearCache {
    NSLog(@"缓存已清理");
    // 简化版本不需要实际的缓存清理
}

// 辅助方法：解析ARM指令
- (NSString *)disassembleInstruction:(uint16_t)instruction {
    if (instruction == 0x0088) {
        return @"lsls r0, r1, #2";
    } else if (instruction == 0x1888) {
        return @"add r0, r1, r2";
    } else if ((instruction & 0xF800) == 0x2000) {
        uint8_t rd = (instruction >> 8) & 0x7;
        uint8_t imm = instruction & 0xFF;
        return [NSString stringWithFormat:@"mov r%u, #%u", rd, imm];
    } else {
        return [NSString stringWithFormat:@"unknown (0x%04X)", instruction];
    }
}

@end
