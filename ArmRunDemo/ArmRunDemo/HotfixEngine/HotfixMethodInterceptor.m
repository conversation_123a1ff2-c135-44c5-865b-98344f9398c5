//
//  HotfixMethodInterceptor.m
//  ArmRunDemo
//
//  Created by <PERSON> on 2025/7/4.
//  Copyright © 2025年 Ming Dai. All rights reserved.
//

#import "HotfixMethodInterceptor.h"
#import <objc/message.h>

/**
 * Hook信息结构
 */
@interface HotfixHookInfo : NSObject
@property (nonatomic, assign) Class targetClass;
@property (nonatomic, assign) SEL selector;
@property (nonatomic, assign) IMP originalImplementation;
@property (nonatomic, assign) IMP newImplementation;
@property (nonatomic, copy) HotfixInterceptorCallback callback;
@property (nonatomic, assign) BOOL isInstanceMethod;
@property (nonatomic, strong) NSString *methodSignature;
@end

@implementation HotfixHookInfo
@end

@interface HotfixMethodInterceptor ()
@property (nonatomic, strong) NSMutableArray<HotfixHookInfo *> *hookInfos;
@property (nonatomic, strong) NSMutableDictionary *callbackMap;
@end

@implementation HotfixMethodInterceptor

+ (instancetype)sharedInstance {
    static HotfixMethodInterceptor *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[HotfixMethodInterceptor alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _hookInfos = [[NSMutableArray alloc] init];
        _callbackMap = [[NSMutableDictionary alloc] init];
    }
    return self;
}

- (BOOL)hookInstanceMethod:(Class)targetClass
                  selector:(SEL)selector
                  callback:(HotfixInterceptorCallback)callback {
    return [self hookMethod:targetClass
                   selector:selector
                   callback:callback
           isInstanceMethod:YES];
}

- (BOOL)hookClassMethod:(Class)targetClass
               selector:(SEL)selector
               callback:(HotfixInterceptorCallback)callback {
    return [self hookMethod:targetClass
                   selector:selector
                   callback:callback
           isInstanceMethod:NO];
}

- (BOOL)hookMethod:(Class)targetClass
          selector:(SEL)selector
          callback:(HotfixInterceptorCallback)callback
  isInstanceMethod:(BOOL)isInstanceMethod {
    
    if (!targetClass || !selector || !callback) {
        NSLog(@"❌ Hook参数无效");
        return NO;
    }
    
    // 检查是否已经Hook过
    if ([self isMethodHooked:targetClass selector:selector]) {
        NSLog(@"⚠️ 方法已被Hook: %@.%@", NSStringFromClass(targetClass), NSStringFromSelector(selector));
        return NO;
    }
    
    Class methodClass = isInstanceMethod ? targetClass : object_getClass(targetClass);
    Method method = isInstanceMethod ? 
        class_getInstanceMethod(targetClass, selector) :
        class_getClassMethod(targetClass, selector);
    
    if (!method) {
        NSLog(@"❌ 未找到方法: %@.%@", NSStringFromClass(targetClass), NSStringFromSelector(selector));
        return NO;
    }
    
    // 获取原始实现
    IMP originalImplementation = method_getImplementation(method);
    const char *typeEncoding = method_getTypeEncoding(method);
    
    // 创建Hook信息
    HotfixHookInfo *hookInfo = [[HotfixHookInfo alloc] init];
    hookInfo.targetClass = targetClass;
    hookInfo.selector = selector;
    hookInfo.originalImplementation = originalImplementation;
    hookInfo.callback = callback;
    hookInfo.isInstanceMethod = isInstanceMethod;
    hookInfo.methodSignature = [NSString stringWithUTF8String:typeEncoding];
    
    // 创建新的实现
    IMP newImplementation = [self createHookImplementationForHookInfo:hookInfo];
    hookInfo.newImplementation = newImplementation;
    
    // 替换方法实现
    class_replaceMethod(methodClass, selector, newImplementation, typeEncoding);
    
    // 保存Hook信息
    [_hookInfos addObject:hookInfo];
    
    NSLog(@"✅ Hook成功: %@.%@", NSStringFromClass(targetClass), NSStringFromSelector(selector));
    return YES;
}

- (IMP)createHookImplementationForHookInfo:(HotfixHookInfo *)hookInfo {
    // 创建一个通用的Hook实现
    // 这里使用block来创建IMP
    id hookBlock = ^id(id self, ...) {
        NSLog(@"🎯 Hook拦截: %@.%@", NSStringFromClass(hookInfo.targetClass), NSStringFromSelector(hookInfo.selector));
        
        // 解析参数
        NSMutableArray *arguments = [[NSMutableArray alloc] init];
        
        // 获取方法签名
        NSMethodSignature *signature = [hookInfo.targetClass instanceMethodSignatureForSelector:hookInfo.selector];
        if (!signature && !hookInfo.isInstanceMethod) {
            signature = [hookInfo.targetClass methodSignatureForSelector:hookInfo.selector];
        }
        
        // 简化处理：只处理基本参数类型
        if (signature) {
            NSUInteger numberOfArguments = [signature numberOfArguments];
            // 跳过self和_cmd参数
            for (NSUInteger i = 2; i < numberOfArguments; i++) {
                const char *argType = [signature getArgumentTypeAtIndex:i];
                if (strcmp(argType, @encode(int)) == 0 || strcmp(argType, @encode(NSInteger)) == 0) {
                    [arguments addObject:@(0)]; // 默认值
                } else if (strcmp(argType, @encode(float)) == 0 || strcmp(argType, @encode(double)) == 0) {
                    [arguments addObject:@(0.0)]; // 默认值
                } else {
                    [arguments addObject:[NSNull null]]; // 其他类型
                }
            }
        }
        
        // 调用回调
        id result = hookInfo.callback(self, hookInfo.selector, arguments);
        
        NSLog(@"🎯 Hook回调完成，结果: %@", result);
        return result;
    };
    
    return imp_implementationWithBlock(hookBlock);
}

- (IMP)replaceInstanceMethod:(Class)targetClass
                    selector:(SEL)selector
           newImplementation:(IMP)newImplementation {
    Method method = class_getInstanceMethod(targetClass, selector);
    if (!method) {
        return NULL;
    }
    
    IMP originalImplementation = method_getImplementation(method);
    const char *typeEncoding = method_getTypeEncoding(method);
    
    class_replaceMethod(targetClass, selector, newImplementation, typeEncoding);
    
    NSLog(@"✅ 替换实例方法: %@.%@", NSStringFromClass(targetClass), NSStringFromSelector(selector));
    return originalImplementation;
}

- (IMP)replaceClassMethod:(Class)targetClass
                 selector:(SEL)selector
        newImplementation:(IMP)newImplementation {
    Method method = class_getClassMethod(targetClass, selector);
    if (!method) {
        return NULL;
    }
    
    IMP originalImplementation = method_getImplementation(method);
    const char *typeEncoding = method_getTypeEncoding(method);
    
    Class metaClass = object_getClass(targetClass);
    class_replaceMethod(metaClass, selector, newImplementation, typeEncoding);
    
    NSLog(@"✅ 替换类方法: %@.%@", NSStringFromClass(targetClass), NSStringFromSelector(selector));
    return originalImplementation;
}

- (BOOL)restoreMethod:(Class)targetClass
             selector:(SEL)selector
originalImplementation:(IMP)originalImplementation {
    if (!targetClass || !selector || !originalImplementation) {
        return NO;
    }
    
    Method method = class_getInstanceMethod(targetClass, selector);
    if (!method) {
        method = class_getClassMethod(targetClass, selector);
    }
    
    if (!method) {
        return NO;
    }
    
    const char *typeEncoding = method_getTypeEncoding(method);
    class_replaceMethod(targetClass, selector, originalImplementation, typeEncoding);
    
    // 从Hook列表中移除
    for (NSInteger i = _hookInfos.count - 1; i >= 0; i--) {
        HotfixHookInfo *info = _hookInfos[i];
        if (info.targetClass == targetClass && info.selector == selector) {
            [_hookInfos removeObjectAtIndex:i];
            break;
        }
    }
    
    NSLog(@"✅ 恢复方法: %@.%@", NSStringFromClass(targetClass), NSStringFromSelector(selector));
    return YES;
}

- (NSDictionary *)parseMethodSignature:(Class)targetClass
                              selector:(SEL)selector
                      isInstanceMethod:(BOOL)isInstanceMethod {
    Method method = isInstanceMethod ? 
        class_getInstanceMethod(targetClass, selector) :
        class_getClassMethod(targetClass, selector);
    
    if (!method) {
        return nil;
    }
    
    const char *typeEncoding = method_getTypeEncoding(method);
    NSArray *argumentTypes = [self getMethodArgumentTypes:method];
    NSString *returnType = [self getMethodReturnType:method];
    
    return @{
        @"typeEncoding": [NSString stringWithUTF8String:typeEncoding],
        @"argumentTypes": argumentTypes,
        @"returnType": returnType,
        @"argumentCount": @(argumentTypes.count)
    };
}

- (NSArray<NSString *> *)getMethodArgumentTypes:(Method)method {
    NSMutableArray *types = [[NSMutableArray alloc] init];
    unsigned int argumentCount = method_getNumberOfArguments(method);
    
    for (unsigned int i = 0; i < argumentCount; i++) {
        char *argType = method_copyArgumentType(method, i);
        if (argType) {
            [types addObject:[NSString stringWithUTF8String:argType]];
            free(argType);
        }
    }
    
    return [types copy];
}

- (NSString *)getMethodReturnType:(Method)method {
    char *returnType = method_copyReturnType(method);
    if (returnType) {
        NSString *returnTypeString = [NSString stringWithUTF8String:returnType];
        free(returnType);
        return returnTypeString;
    }
    return @"v"; // void
}

- (BOOL)isMethodHooked:(Class)targetClass selector:(SEL)selector {
    for (HotfixHookInfo *info in _hookInfos) {
        if (info.targetClass == targetClass && info.selector == selector) {
            return YES;
        }
    }
    return NO;
}

- (NSArray<NSDictionary *> *)getAllHookedMethods {
    NSMutableArray *result = [[NSMutableArray alloc] init];
    
    for (HotfixHookInfo *info in _hookInfos) {
        [result addObject:@{
            @"class": NSStringFromClass(info.targetClass),
            @"selector": NSStringFromSelector(info.selector),
            @"isInstanceMethod": @(info.isInstanceMethod),
            @"methodSignature": info.methodSignature ?: @""
        }];
    }
    
    return [result copy];
}

- (void)clearAllHooks {
    // 恢复所有Hook的方法
    for (HotfixHookInfo *info in _hookInfos) {
        [self restoreMethod:info.targetClass
                   selector:info.selector
      originalImplementation:info.originalImplementation];
    }
    
    [_hookInfos removeAllObjects];
    [_callbackMap removeAllObjects];
    
    NSLog(@"✅ 清理所有Hook完成");
}

@end
