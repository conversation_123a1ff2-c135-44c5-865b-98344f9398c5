//
//  HotfixMethodInterceptor.h
//  ArmRunDemo
//
//  Created by <PERSON> on 2025/7/4.
//  Copyright © 2025年 Ming Dai. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <objc/runtime.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * 方法拦截回调类型
 */
typedef id _Nullable (^HotfixInterceptorCallback)(id target, SEL selector, NSArray *arguments);

/**
 * 方法拦截器
 * 提供基于Objective-C runtime的方法hook和替换功能
 */
@interface HotfixMethodInterceptor : NSObject

/**
 * 单例实例
 */
+ (instancetype)sharedInstance;

/**
 * Hook实例方法
 * @param targetClass 目标类
 * @param selector 目标方法选择器
 * @param callback 拦截回调
 * @return 是否Hook成功
 */
- (BOOL)hookInstanceMethod:(Class)targetClass
                  selector:(SEL)selector
                  callback:(HotfixInterceptorCallback)callback;

/**
 * Hook类方法
 * @param targetClass 目标类
 * @param selector 目标方法选择器
 * @param callback 拦截回调
 * @return 是否Hook成功
 */
- (BOOL)hookClassMethod:(Class)targetClass
               selector:(SEL)selector
               callback:(HotfixInterceptorCallback)callback;

/**
 * 替换实例方法实现
 * @param targetClass 目标类
 * @param selector 目标方法选择器
 * @param newImplementation 新的方法实现
 * @return 原始方法实现
 */
- (IMP _Nullable)replaceInstanceMethod:(Class)targetClass
                              selector:(SEL)selector
                     newImplementation:(IMP)newImplementation;

/**
 * 替换类方法实现
 * @param targetClass 目标类
 * @param selector 目标方法选择器
 * @param newImplementation 新的方法实现
 * @return 原始方法实现
 */
- (IMP _Nullable)replaceClassMethod:(Class)targetClass
                           selector:(SEL)selector
                  newImplementation:(IMP)newImplementation;

/**
 * 恢复方法原始实现
 * @param targetClass 目标类
 * @param selector 目标方法选择器
 * @param originalImplementation 原始方法实现
 * @return 是否恢复成功
 */
- (BOOL)restoreMethod:(Class)targetClass
             selector:(SEL)selector
originalImplementation:(IMP)originalImplementation;

/**
 * 解析方法签名
 * @param targetClass 目标类
 * @param selector 目标方法选择器
 * @param isInstanceMethod 是否为实例方法
 * @return 方法签名信息字典
 */
- (NSDictionary *)parseMethodSignature:(Class)targetClass
                              selector:(SEL)selector
                      isInstanceMethod:(BOOL)isInstanceMethod;

/**
 * 获取方法参数类型列表
 * @param method 方法对象
 * @return 参数类型数组
 */
- (NSArray<NSString *> *)getMethodArgumentTypes:(Method)method;

/**
 * 获取方法返回值类型
 * @param method 方法对象
 * @return 返回值类型字符串
 */
- (NSString *)getMethodReturnType:(Method)method;

/**
 * 检查方法是否已被Hook
 * @param targetClass 目标类
 * @param selector 目标方法选择器
 * @return 是否已被Hook
 */
- (BOOL)isMethodHooked:(Class)targetClass selector:(SEL)selector;

/**
 * 获取所有已Hook的方法信息
 * @return Hook信息数组
 */
- (NSArray<NSDictionary *> *)getAllHookedMethods;

/**
 * 清理所有Hook
 */
- (void)clearAllHooks;

@end

NS_ASSUME_NONNULL_END
