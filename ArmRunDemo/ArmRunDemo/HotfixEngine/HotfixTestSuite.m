//
//  HotfixTestSuite.m
//  ArmRunDemo
//
//  Created by <PERSON> on 2025/7/4.
//  Copyright © 2025年 Ming Dai. All rights reserved.
//

#import "HotfixTestSuite.h"
#import "SimpleDynarmicBridge.h"
#import "HotfixMethodInterceptor.h"
#import "DynamicCodeLoader.h"
#import "HotfixManager.h"
#import "ARMCodeGenerator.h"

@implementation HotfixTestResult
@end

@interface HotfixTestSuite ()
@property (nonatomic, copy) void (^progressCallback)(NSString *testName, BOOL completed);
@end

@implementation HotfixTestSuite

+ (instancetype)sharedInstance {
    static HotfixTestSuite *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[HotfixTestSuite alloc] init];
    });
    return instance;
}

- (NSArray<HotfixTestResult *> *)runAllTests {
    NSMutableArray *allResults = [[NSMutableArray alloc] init];
    
    NSLog(@"🧪 开始运行完整测试套件...");
    
    // 运行各类测试
    [allResults addObjectsFromArray:[self runBasicTests]];
    [allResults addObjectsFromArray:[self runARMExecutionTests]];
    [allResults addObjectsFromArray:[self runMethodInterceptionTests]];
    [allResults addObjectsFromArray:[self runCodeLoadingTests]];
    [allResults addObjectsFromArray:[self runHotfixManagementTests]];
    [allResults addObjectsFromArray:[self runPerformanceTests]];
    [allResults addObjectsFromArray:[self runExceptionHandlingTests]];
    
    NSLog(@"🧪 测试套件运行完成");
    return [allResults copy];
}

- (NSArray<HotfixTestResult *> *)runBasicTests {
    NSMutableArray *results = [[NSMutableArray alloc] init];
    
    // 测试1: 组件初始化
    [results addObject:[self testComponentInitialization]];
    
    // 测试2: 基础配置
    [results addObject:[self testBasicConfiguration]];
    
    return [results copy];
}

- (HotfixTestResult *)testComponentInitialization {
    NSDate *startTime = [NSDate date];
    HotfixTestResult *result = [[HotfixTestResult alloc] init];
    result.testName = @"组件初始化测试";
    
    [self notifyProgress:result.testName completed:NO];
    
    @try {
        // 测试SimpleDynarmicBridge初始化
        SimpleDynarmicBridge *armBridge = [SimpleDynarmicBridge sharedInstance];
        BOOL armInitialized = [armBridge initializeEngine];
        
        // 测试HotfixManager初始化
        HotfixManager *hotfixManager = [HotfixManager sharedInstance];
        BOOL hotfixInitialized = [hotfixManager initialize];
        
        // 测试其他组件
        HotfixMethodInterceptor *interceptor = [HotfixMethodInterceptor sharedInstance];
        DynamicCodeLoader *codeLoader = [DynamicCodeLoader sharedInstance];
        ARMCodeGenerator *codeGenerator = [ARMCodeGenerator sharedInstance];
        
        BOOL allInitialized = armInitialized && hotfixInitialized && 
                             interceptor && codeLoader && codeGenerator;
        
        result.passed = allInitialized;
        result.message = allInitialized ? @"所有组件初始化成功" : @"部分组件初始化失败";
        result.details = @{
            @"armBridge": @(armInitialized),
            @"hotfixManager": @(hotfixInitialized),
            @"interceptor": @(interceptor != nil),
            @"codeLoader": @(codeLoader != nil),
            @"codeGenerator": @(codeGenerator != nil)
        };
        
    } @catch (NSException *exception) {
        result.passed = NO;
        result.message = [NSString stringWithFormat:@"初始化异常: %@", exception.reason];
    }
    
    result.duration = [[NSDate date] timeIntervalSinceDate:startTime];
    [self notifyProgress:result.testName completed:YES];
    
    return result;
}

- (HotfixTestResult *)testBasicConfiguration {
    NSDate *startTime = [NSDate date];
    HotfixTestResult *result = [[HotfixTestResult alloc] init];
    result.testName = @"基础配置测试";
    
    [self notifyProgress:result.testName completed:NO];
    
    @try {
        HotfixManager *manager = [HotfixManager sharedInstance];
        
        // 测试调试模式设置
        [manager setDebugMode:YES];
        
        // 测试统计信息获取
        NSDictionary *stats = [manager getHotfixStats];
        
        BOOL configValid = stats != nil && [stats isKindOfClass:[NSDictionary class]];
        
        result.passed = configValid;
        result.message = configValid ? @"基础配置正常" : @"基础配置异常";
        result.details = @{@"stats": stats ?: @{}};
        
    } @catch (NSException *exception) {
        result.passed = NO;
        result.message = [NSString stringWithFormat:@"配置异常: %@", exception.reason];
    }
    
    result.duration = [[NSDate date] timeIntervalSinceDate:startTime];
    [self notifyProgress:result.testName completed:YES];
    
    return result;
}

- (NSArray<HotfixTestResult *> *)runARMExecutionTests {
    NSMutableArray *results = [[NSMutableArray alloc] init];
    
    // 测试ARM代码执行
    [results addObject:[self testARMCodeExecution]];
    
    // 测试不同指令类型
    [results addObject:[self testDifferentInstructions]];
    
    return [results copy];
}

- (HotfixTestResult *)testARMCodeExecution {
    NSDate *startTime = [NSDate date];
    HotfixTestResult *result = [[HotfixTestResult alloc] init];
    result.testName = @"ARM代码执行测试";
    
    [self notifyProgress:result.testName completed:NO];
    
    @try {
        SimpleDynarmicBridge *bridge = [SimpleDynarmicBridge sharedInstance];
        
        // 测试LSLS指令
        uint16_t lslsCode[] = {0x0088}; // lsls r0, r1, #2
        NSData *codeData = [NSData dataWithBytes:lslsCode length:sizeof(lslsCode)];
        NSArray *params = @[@0, @2]; // r0=0, r1=2
        
        id executionResult = nil;
        BOOL success = [bridge executeARMCode:codeData withParams:params result:&executionResult];
        
        BOOL resultCorrect = success && [executionResult intValue] == 8; // 2 << 2 = 8
        
        result.passed = resultCorrect;
        result.message = resultCorrect ? @"ARM代码执行正确" : @"ARM代码执行结果错误";
        result.details = @{
            @"success": @(success),
            @"expected": @8,
            @"actual": executionResult ?: @0
        };
        
    } @catch (NSException *exception) {
        result.passed = NO;
        result.message = [NSString stringWithFormat:@"执行异常: %@", exception.reason];
    }
    
    result.duration = [[NSDate date] timeIntervalSinceDate:startTime];
    [self notifyProgress:result.testName completed:YES];
    
    return result;
}

- (HotfixTestResult *)testDifferentInstructions {
    NSDate *startTime = [NSDate date];
    HotfixTestResult *result = [[HotfixTestResult alloc] init];
    result.testName = @"多种指令测试";
    
    [self notifyProgress:result.testName completed:NO];
    
    @try {
        SimpleDynarmicBridge *bridge = [SimpleDynarmicBridge sharedInstance];
        NSMutableArray *testResults = [[NSMutableArray alloc] init];
        
        // 测试MOV指令
        uint16_t movCode[] = {0x202A}; // mov r0, #42
        NSData *movData = [NSData dataWithBytes:movCode length:sizeof(movCode)];
        id movResult = nil;
        BOOL movSuccess = [bridge executeARMCode:movData withParams:@[] result:&movResult];
        [testResults addObject:@{@"instruction": @"MOV", @"success": @(movSuccess), @"result": movResult ?: @0}];
        
        // 测试ADD指令
        [bridge reset];
        uint16_t addCode[] = {0x1888}; // add r0, r1, r2
        NSData *addData = [NSData dataWithBytes:addCode length:sizeof(addCode)];
        NSArray *addParams = @[@0, @10, @20];
        id addResult = nil;
        BOOL addSuccess = [bridge executeARMCode:addData withParams:addParams result:&addResult];
        [testResults addObject:@{@"instruction": @"ADD", @"success": @(addSuccess), @"result": addResult ?: @0}];
        
        BOOL allPassed = movSuccess && addSuccess;
        
        result.passed = allPassed;
        result.message = allPassed ? @"多种指令测试通过" : @"部分指令测试失败";
        result.details = @{@"instructions": testResults};
        
    } @catch (NSException *exception) {
        result.passed = NO;
        result.message = [NSString stringWithFormat:@"指令测试异常: %@", exception.reason];
    }
    
    result.duration = [[NSDate date] timeIntervalSinceDate:startTime];
    [self notifyProgress:result.testName completed:YES];
    
    return result;
}

- (NSArray<HotfixTestResult *> *)runMethodInterceptionTests {
    NSMutableArray *results = [[NSMutableArray alloc] init];
    
    // 测试方法Hook
    [results addObject:[self testMethodHook]];
    
    return [results copy];
}

- (HotfixTestResult *)testMethodHook {
    NSDate *startTime = [NSDate date];
    HotfixTestResult *result = [[HotfixTestResult alloc] init];
    result.testName = @"方法Hook测试";
    
    [self notifyProgress:result.testName completed:NO];
    
    @try {
        HotfixMethodInterceptor *interceptor = [HotfixMethodInterceptor sharedInstance];
        
        // 创建测试回调
        __block BOOL callbackExecuted = NO;
        HotfixInterceptorCallback callback = ^id(id target, SEL selector, NSArray *arguments) {
            callbackExecuted = YES;
            return @"Hooked!";
        };
        
        // Hook一个测试方法
        Class testClass = [NSString class];
        SEL testSelector = @selector(description);
        
        BOOL hookSuccess = [interceptor hookInstanceMethod:testClass selector:testSelector callback:callback];
        
        result.passed = hookSuccess;
        result.message = hookSuccess ? @"方法Hook成功" : @"方法Hook失败";
        result.details = @{
            @"hookSuccess": @(hookSuccess),
            @"callbackExecuted": @(callbackExecuted)
        };
        
        // 清理Hook
        if (hookSuccess) {
            [interceptor clearAllHooks];
        }
        
    } @catch (NSException *exception) {
        result.passed = NO;
        result.message = [NSString stringWithFormat:@"Hook测试异常: %@", exception.reason];
    }
    
    result.duration = [[NSDate date] timeIntervalSinceDate:startTime];
    [self notifyProgress:result.testName completed:YES];
    
    return result;
}

- (NSArray<HotfixTestResult *> *)runCodeLoadingTests {
    NSMutableArray *results = [[NSMutableArray alloc] init];
    
    // 测试代码加载
    [results addObject:[self testCodeLoading]];
    
    return [results copy];
}

- (HotfixTestResult *)testCodeLoading {
    NSDate *startTime = [NSDate date];
    HotfixTestResult *result = [[HotfixTestResult alloc] init];
    result.testName = @"代码加载测试";
    
    [self notifyProgress:result.testName completed:NO];
    
    @try {
        DynamicCodeLoader *loader = [DynamicCodeLoader sharedInstance];
        
        // 创建测试代码
        uint16_t testCode[] = {0x2001, 0x2002}; // mov r0, #1; mov r0, #2
        NSData *codeData = [NSData dataWithBytes:testCode length:sizeof(testCode)];
        
        // 加载代码
        CodeSegmentInfo *segment = [loader loadCode:codeData 
                                         identifier:@"test_segment" 
                                        permissions:CodePermissionRead | CodePermissionExecute];
        
        BOOL loadSuccess = segment != nil;
        
        // 清理
        if (loadSuccess) {
            [loader unloadCode:@"test_segment"];
        }
        
        result.passed = loadSuccess;
        result.message = loadSuccess ? @"代码加载成功" : @"代码加载失败";
        result.details = @{@"segmentLoaded": @(loadSuccess)};
        
    } @catch (NSException *exception) {
        result.passed = NO;
        result.message = [NSString stringWithFormat:@"代码加载异常: %@", exception.reason];
    }
    
    result.duration = [[NSDate date] timeIntervalSinceDate:startTime];
    [self notifyProgress:result.testName completed:YES];
    
    return result;
}

- (NSArray<HotfixTestResult *> *)runHotfixManagementTests {
    NSMutableArray *results = [[NSMutableArray alloc] init];
    
    // 测试补丁管理
    [results addObject:[self testPatchManagement]];
    
    return [results copy];
}

- (HotfixTestResult *)testPatchManagement {
    NSDate *startTime = [NSDate date];
    HotfixTestResult *result = [[HotfixTestResult alloc] init];
    result.testName = @"补丁管理测试";
    
    [self notifyProgress:result.testName completed:NO];
    
    @try {
        HotfixManager *manager = [HotfixManager sharedInstance];
        
        // 创建测试补丁
        NSDictionary *patchDict = @{
            @"patchId": @"test_management_patch",
            @"version": @"1.0.0",
            @"name": @"管理测试补丁",
            @"description": @"用于测试补丁管理功能",
            @"type": @"methodReplace",
            @"targetClass": @"NSObject",
            @"targetMethod": @"description",
            @"isInstanceMethod": @YES,
            @"methodSignature": @"@@:"
        };
        
        NSData *patchData = [NSJSONSerialization dataWithJSONObject:patchDict options:0 error:nil];
        HotfixPatch *patch = [manager loadPatchFromJSON:patchData];
        
        BOOL patchLoaded = patch != nil;
        
        // 清理
        if (patchLoaded) {
            [manager removePatch:patch.patchId];
        }
        
        result.passed = patchLoaded;
        result.message = patchLoaded ? @"补丁管理正常" : @"补丁管理异常";
        result.details = @{@"patchLoaded": @(patchLoaded)};
        
    } @catch (NSException *exception) {
        result.passed = NO;
        result.message = [NSString stringWithFormat:@"补丁管理异常: %@", exception.reason];
    }
    
    result.duration = [[NSDate date] timeIntervalSinceDate:startTime];
    [self notifyProgress:result.testName completed:YES];
    
    return result;
}

- (NSArray<HotfixTestResult *> *)runPerformanceTests {
    NSMutableArray *results = [[NSMutableArray alloc] init];
    
    // 性能测试相对简单，主要测试执行时间
    [results addObject:[self testExecutionPerformance]];
    
    return [results copy];
}

- (HotfixTestResult *)testExecutionPerformance {
    NSDate *startTime = [NSDate date];
    HotfixTestResult *result = [[HotfixTestResult alloc] init];
    result.testName = @"执行性能测试";
    
    [self notifyProgress:result.testName completed:NO];
    
    @try {
        SimpleDynarmicBridge *bridge = [SimpleDynarmicBridge sharedInstance];
        
        // 执行多次ARM代码测试性能
        uint16_t testCode[] = {0x0088}; // lsls r0, r1, #2
        NSData *codeData = [NSData dataWithBytes:testCode length:sizeof(testCode)];
        NSArray *params = @[@0, @2];
        
        NSDate *perfStart = [NSDate date];
        NSInteger iterations = 100;
        NSInteger successCount = 0;
        
        for (NSInteger i = 0; i < iterations; i++) {
            id execResult = nil;
            BOOL success = [bridge executeARMCode:codeData withParams:params result:&execResult];
            if (success) successCount++;
        }
        
        NSTimeInterval totalTime = [[NSDate date] timeIntervalSinceDate:perfStart];
        double avgTime = totalTime / iterations * 1000; // 毫秒
        
        BOOL performanceGood = avgTime < 10.0; // 平均每次执行小于10毫秒
        
        result.passed = performanceGood && (successCount == iterations);
        result.message = [NSString stringWithFormat:@"平均执行时间: %.2fms", avgTime];
        result.details = @{
            @"iterations": @(iterations),
            @"successCount": @(successCount),
            @"totalTime": @(totalTime),
            @"avgTimeMs": @(avgTime)
        };
        
    } @catch (NSException *exception) {
        result.passed = NO;
        result.message = [NSString stringWithFormat:@"性能测试异常: %@", exception.reason];
    }
    
    result.duration = [[NSDate date] timeIntervalSinceDate:startTime];
    [self notifyProgress:result.testName completed:YES];
    
    return result;
}

- (NSArray<HotfixTestResult *> *)runExceptionHandlingTests {
    NSMutableArray *results = [[NSMutableArray alloc] init];
    
    // 测试异常处理
    [results addObject:[self testExceptionHandling]];
    
    return [results copy];
}

- (HotfixTestResult *)testExceptionHandling {
    NSDate *startTime = [NSDate date];
    HotfixTestResult *result = [[HotfixTestResult alloc] init];
    result.testName = @"异常处理测试";
    
    [self notifyProgress:result.testName completed:NO];
    
    @try {
        SimpleDynarmicBridge *bridge = [SimpleDynarmicBridge sharedInstance];
        
        // 测试无效代码处理
        NSData *invalidCode = [NSData data]; // 空代码
        id execResult = nil;
        BOOL handled = [bridge executeARMCode:invalidCode withParams:@[] result:&execResult];
        
        // 应该返回NO而不是崩溃
        BOOL exceptionHandled = !handled; // 期望失败但不崩溃
        
        result.passed = exceptionHandled;
        result.message = exceptionHandled ? @"异常处理正常" : @"异常处理失败";
        result.details = @{@"invalidCodeHandled": @(exceptionHandled)};
        
    } @catch (NSException *exception) {
        // 如果捕获到异常，说明异常处理不够好
        result.passed = NO;
        result.message = [NSString stringWithFormat:@"未处理异常: %@", exception.reason];
    }
    
    result.duration = [[NSDate date] timeIntervalSinceDate:startTime];
    [self notifyProgress:result.testName completed:YES];
    
    return result;
}

- (NSString *)generateTestReport:(NSArray<HotfixTestResult *> *)results {
    NSMutableString *report = [[NSMutableString alloc] init];
    
    [report appendString:@"=== 热修复功能测试报告 ===\n\n"];
    
    NSDictionary *stats = [self getTestStatistics:results];
    [report appendFormat:@"测试总数: %@\n", stats[@"total"]];
    [report appendFormat:@"通过: %@\n", stats[@"passed"]];
    [report appendFormat:@"失败: %@\n", stats[@"failed"]];
    [report appendFormat:@"成功率: %.1f%%\n", [stats[@"successRate"] doubleValue]];
    [report appendFormat:@"总耗时: %.3fs\n\n", [stats[@"totalDuration"] doubleValue]];
    
    [report appendString:@"详细结果:\n"];
    for (HotfixTestResult *result in results) {
        NSString *status = result.passed ? @"✅" : @"❌";
        [report appendFormat:@"%@ %@ (%.3fs) - %@\n", 
         status, result.testName, result.duration, result.message];
    }
    
    return [report copy];
}

- (NSDictionary *)getTestStatistics:(NSArray<HotfixTestResult *> *)results {
    NSInteger total = results.count;
    NSInteger passed = 0;
    NSTimeInterval totalDuration = 0;
    
    for (HotfixTestResult *result in results) {
        if (result.passed) passed++;
        totalDuration += result.duration;
    }
    
    double successRate = total > 0 ? (double)passed / total * 100.0 : 0.0;
    
    return @{
        @"total": @(total),
        @"passed": @(passed),
        @"failed": @(total - passed),
        @"successRate": @(successRate),
        @"totalDuration": @(totalDuration)
    };
}

- (void)setTestProgressCallback:(void (^)(NSString *, BOOL))callback {
    self.progressCallback = callback;
}

- (void)notifyProgress:(NSString *)testName completed:(BOOL)completed {
    if (self.progressCallback) {
        self.progressCallback(testName, completed);
    }
}

@end
