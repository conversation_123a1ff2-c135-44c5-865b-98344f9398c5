//
//  DynamicCodeLoader.h
//  ArmRunDemo
//
//  Created by <PERSON> on 2025/7/4.
//  Copyright © 2025年 Ming Dai. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * 代码段权限类型
 */
typedef NS_OPTIONS(NSUInteger, CodePermission) {
    CodePermissionRead    = 1 << 0,  // 可读
    CodePermissionWrite   = 1 << 1,  // 可写
    CodePermissionExecute = 1 << 2   // 可执行
};

/**
 * 代码加载状态
 */
typedef NS_ENUM(NSUInteger, CodeLoadStatus) {
    CodeLoadStatusUnloaded,  // 未加载
    CodeLoadStatusLoaded,    // 已加载
    CodeLoadStatusExecuting, // 执行中
    CodeLoadStatusError      // 错误状态
};

/**
 * 代码段信息
 */
@interface CodeSegmentInfo : NSObject
@property (nonatomic, strong) NSString *identifier;
@property (nonatomic, strong) NSData *machineCode;
@property (nonatomic, assign) void *baseAddress;
@property (nonatomic, assign) size_t size;
@property (nonatomic, assign) CodePermission permissions;
@property (nonatomic, assign) CodeLoadStatus status;
@property (nonatomic, strong) NSDate *loadTime;
@property (nonatomic, strong) NSDictionary *metadata;
@property (nonatomic, assign) BOOL enabled;
@end

/**
 * 动态代码加载器
 * 支持从内存加载ARM机器码，管理代码段权限和验证
 */
@interface DynamicCodeLoader : NSObject

/**
 * 单例实例
 */
+ (instancetype)sharedInstance;

/**
 * 加载ARM机器码到内存
 * @param machineCode ARM机器码数据
 * @param identifier 代码段标识符
 * @param permissions 权限设置
 * @return 代码段信息，失败返回nil
 */
- (CodeSegmentInfo * _Nullable)loadCode:(NSData *)machineCode
                             identifier:(NSString *)identifier
                            permissions:(CodePermission)permissions;

/**
 * 从文件加载ARM机器码
 * @param filePath 文件路径
 * @param identifier 代码段标识符
 * @param permissions 权限设置
 * @return 代码段信息，失败返回nil
 */
- (CodeSegmentInfo * _Nullable)loadCodeFromFile:(NSString *)filePath
                                     identifier:(NSString *)identifier
                                    permissions:(CodePermission)permissions;

/**
 * 卸载代码段
 * @param identifier 代码段标识符
 * @return 是否卸载成功
 */
- (BOOL)unloadCode:(NSString *)identifier;

/**
 * 获取代码段信息
 * @param identifier 代码段标识符
 * @return 代码段信息，不存在返回nil
 */
- (CodeSegmentInfo * _Nullable)getCodeSegmentInfo:(NSString *)identifier;

/**
 * 获取代码段执行地址
 * @param identifier 代码段标识符
 * @return 执行地址，失败返回NULL
 */
- (void * _Nullable)getExecutionAddress:(NSString *)identifier;

/**
 * 设置代码段权限
 * @param identifier 代码段标识符
 * @param permissions 新的权限设置
 * @return 是否设置成功
 */
- (BOOL)setCodePermissions:(NSString *)identifier permissions:(CodePermission)permissions;

/**
 * 验证ARM代码的安全性
 * @param machineCode ARM机器码数据
 * @return 验证结果字典
 */
- (NSDictionary *)validateCodeSecurity:(NSData *)machineCode;

/**
 * 验证代码完整性
 * @param machineCode ARM机器码数据
 * @param expectedHash 期望的哈希值
 * @return 是否验证通过
 */
- (BOOL)validateCodeIntegrity:(NSData *)machineCode expectedHash:(NSString *)expectedHash;

/**
 * 计算代码哈希值
 * @param machineCode ARM机器码数据
 * @return SHA256哈希值
 */
- (NSString *)calculateCodeHash:(NSData *)machineCode;

/**
 * 检查代码是否包含危险指令
 * @param machineCode ARM机器码数据
 * @return 危险指令列表
 */
- (NSArray<NSString *> *)scanForDangerousInstructions:(NSData *)machineCode;

/**
 * 获取所有已加载的代码段
 * @return 代码段信息数组
 */
- (NSArray<CodeSegmentInfo *> *)getAllLoadedSegments;

/**
 * 清理所有代码段
 */
- (void)clearAllSegments;

/**
 * 获取内存使用统计
 * @return 内存使用信息字典
 */
- (NSDictionary *)getMemoryUsageStats;

/**
 * 设置代码段元数据
 * @param identifier 代码段标识符
 * @param metadata 元数据字典
 * @return 是否设置成功
 */
- (BOOL)setCodeMetadata:(NSString *)identifier metadata:(NSDictionary *)metadata;

/**
 * 启用/禁用代码段
 * @param identifier 代码段标识符
 * @param enabled 是否启用
 * @return 是否操作成功
 */
- (BOOL)setCodeSegmentEnabled:(NSString *)identifier enabled:(BOOL)enabled;

/**
 * 检查代码段是否可执行
 * @param identifier 代码段标识符
 * @return 是否可执行
 */
- (BOOL)isCodeSegmentExecutable:(NSString *)identifier;

@end

NS_ASSUME_NONNULL_END
