//
//  HotfixManager.m
//  ArmRunDemo
//
//  Created by <PERSON> on 2025/7/4.
//  Copyright © 2025年 Ming Dai. All rights reserved.
//

#import "HotfixManager.h"
#import "HotfixMethodInterceptor.h"
#import "ARMCodeGenerator.h"
#import "DynamicCodeLoader.h"
#import "SimpleDynarmicBridge.h"

@implementation HotfixPatch
@end

@interface HotfixManager ()
@property (nonatomic, strong) NSMutableDictionary<NSString *, HotfixPatch *> *patches;
@property (nonatomic, strong) NSMutableDictionary<NSString *, id> *originalImplementations;
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSString *> *backups;
@property (nonatomic, strong) HotfixMethodInterceptor *methodInterceptor;
@property (nonatomic, strong) ARMCodeGenerator *codeGenerator;
@property (nonatomic, strong) DynamicCodeLoader *codeLoader;
@property (nonatomic, strong) SimpleDynarmicBridge *armBridge;
@property (nonatomic, assign) BOOL debugMode;
@property (nonatomic, assign) NSUInteger appliedPatchCount;
@property (nonatomic, assign) NSUInteger failedPatchCount;
@end

@implementation HotfixManager

+ (instancetype)sharedInstance {
    static HotfixManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[HotfixManager alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _patches = [[NSMutableDictionary alloc] init];
        _originalImplementations = [[NSMutableDictionary alloc] init];
        _backups = [[NSMutableDictionary alloc] init];
        _debugMode = NO;
        _appliedPatchCount = 0;
        _failedPatchCount = 0;
    }
    return self;
}

- (BOOL)initialize {
    NSLog(@"🔧 初始化热修复管理器...");
    
    // 初始化各个组件
    _methodInterceptor = [HotfixMethodInterceptor sharedInstance];
    _codeGenerator = [ARMCodeGenerator sharedInstance];
    _codeLoader = [DynamicCodeLoader sharedInstance];
    _armBridge = [SimpleDynarmicBridge sharedInstance];
    
    // 初始化ARM执行引擎
    BOOL armInitialized = [_armBridge initializeEngine];
    if (!armInitialized) {
        NSLog(@"❌ ARM执行引擎初始化失败");
        return NO;
    }
    
    NSLog(@"✅ 热修复管理器初始化成功");
    return YES;
}

- (HotfixPatch *)loadPatchFromJSON:(NSData *)jsonData {
    if (!jsonData) {
        NSLog(@"❌ JSON数据为空");
        return nil;
    }
    
    NSError *error;
    NSDictionary *patchDict = [NSJSONSerialization JSONObjectWithData:jsonData options:0 error:&error];
    if (error) {
        NSLog(@"❌ JSON解析失败: %@", error.localizedDescription);
        return nil;
    }
    
    return [self createPatchFromDictionary:patchDict];
}

- (HotfixPatch *)loadPatchFromFile:(NSString *)filePath {
    NSData *fileData = [NSData dataWithContentsOfFile:filePath];
    if (!fileData) {
        NSLog(@"❌ 无法读取补丁文件: %@", filePath);
        return nil;
    }
    
    NSLog(@"📁 从文件加载补丁: %@", filePath);
    return [self loadPatchFromJSON:fileData];
}

- (HotfixPatch *)createPatchFromDictionary:(NSDictionary *)dict {
    HotfixPatch *patch = [[HotfixPatch alloc] init];
    
    patch.patchId = dict[@"patchId"];
    patch.version = dict[@"version"];
    patch.name = dict[@"name"];
    patch.description = dict[@"description"];
    patch.targetClass = dict[@"targetClass"];
    patch.targetMethod = dict[@"targetMethod"];
    patch.isInstanceMethod = [dict[@"isInstanceMethod"] boolValue];
    patch.methodSignature = dict[@"methodSignature"];
    patch.createTime = [NSDate date];
    patch.status = HotfixPatchStatusPending;
    patch.metadata = dict[@"metadata"];
    
    // 解析补丁类型
    NSString *typeString = dict[@"type"];
    if ([typeString isEqualToString:@"methodReplace"]) {
        patch.type = HotfixPatchTypeMethodReplace;
    } else if ([typeString isEqualToString:@"methodHook"]) {
        patch.type = HotfixPatchTypeMethodHook;
    } else if ([typeString isEqualToString:@"codeInjection"]) {
        patch.type = HotfixPatchTypeCodeInjection;
    } else {
        patch.type = HotfixPatchTypeBugfix;
    }
    
    // 解析ARM代码
    NSString *armCodeBase64 = dict[@"armCode"];
    if (armCodeBase64) {
        patch.armCode = [[NSData alloc] initWithBase64EncodedString:armCodeBase64 options:0];
    }
    
    // 计算哈希
    patch.hash = [self calculatePatchHash:patch];
    
    if (!patch.patchId || !patch.targetClass || !patch.targetMethod) {
        NSLog(@"❌ 补丁信息不完整");
        return nil;
    }
    
    NSLog(@"✅ 补丁加载成功: %@", patch.patchId);
    return patch;
}

- (NSString *)calculatePatchHash:(HotfixPatch *)patch {
    NSString *hashInput = [NSString stringWithFormat:@"%@_%@_%@_%@", 
                          patch.patchId, patch.targetClass, patch.targetMethod, patch.version];
    
    // 简化的哈希计算
    return [NSString stringWithFormat:@"%08lX", (unsigned long)[hashInput hash]];
}

- (BOOL)applyPatch:(HotfixPatch *)patch {
    if (!patch) {
        NSLog(@"❌ 补丁对象为空");
        return NO;
    }
    
    NSLog(@"🔧 开始应用补丁: %@", patch.patchId);
    
    // 验证补丁
    NSDictionary *validation = [self validatePatch:patch];
    if (![validation[@"valid"] boolValue]) {
        NSLog(@"❌ 补丁验证失败: %@", validation[@"reason"]);
        patch.status = HotfixPatchStatusFailed;
        _failedPatchCount++;
        return NO;
    }
    
    // 检查冲突
    NSArray *conflicts = [self checkPatchConflicts:patch];
    if (conflicts.count > 0) {
        NSLog(@"❌ 补丁冲突: %@", conflicts);
        patch.status = HotfixPatchStatusFailed;
        _failedPatchCount++;
        return NO;
    }
    
    // 获取目标类
    Class targetClass = NSClassFromString(patch.targetClass);
    if (!targetClass) {
        NSLog(@"❌ 目标类不存在: %@", patch.targetClass);
        patch.status = HotfixPatchStatusFailed;
        _failedPatchCount++;
        return NO;
    }
    
    SEL targetSelector = NSSelectorFromString(patch.targetMethod);
    if (!targetSelector) {
        NSLog(@"❌ 目标方法选择器无效: %@", patch.targetMethod);
        patch.status = HotfixPatchStatusFailed;
        _failedPatchCount++;
        return NO;
    }
    
    BOOL success = NO;
    
    switch (patch.type) {
        case HotfixPatchTypeMethodReplace:
            success = [self applyMethodReplacePatch:patch targetClass:targetClass selector:targetSelector];
            break;
        case HotfixPatchTypeMethodHook:
            success = [self applyMethodHookPatch:patch targetClass:targetClass selector:targetSelector];
            break;
        case HotfixPatchTypeCodeInjection:
            success = [self applyCodeInjectionPatch:patch targetClass:targetClass selector:targetSelector];
            break;
        default:
            NSLog(@"❌ 不支持的补丁类型: %lu", (unsigned long)patch.type);
            break;
    }
    
    if (success) {
        patch.status = HotfixPatchStatusApplied;
        patch.applyTime = [NSDate date];
        _patches[patch.patchId] = patch;
        _appliedPatchCount++;
        NSLog(@"✅ 补丁应用成功: %@", patch.patchId);
    } else {
        patch.status = HotfixPatchStatusFailed;
        _failedPatchCount++;
        NSLog(@"❌ 补丁应用失败: %@", patch.patchId);
    }
    
    return success;
}

- (BOOL)applyMethodReplacePatch:(HotfixPatch *)patch 
                    targetClass:(Class)targetClass 
                       selector:(SEL)selector {
    
    NSLog(@"🔄 应用方法替换补丁: %@.%@", patch.targetClass, patch.targetMethod);
    
    // 创建新的方法实现
    HotfixInterceptorCallback callback = ^id(id target, SEL sel, NSArray *arguments) {
        NSLog(@"🎯 执行补丁方法: %@.%@", NSStringFromClass([target class]), NSStringFromSelector(sel));
        
        // 如果有ARM代码，执行ARM代码
        if (patch.armCode) {
            id result = nil;
            BOOL success = [self.armBridge executeARMCode:patch.armCode withParams:arguments result:&result];
            if (success) {
                NSLog(@"✅ ARM代码执行成功，结果: %@", result);
                return result;
            } else {
                NSLog(@"❌ ARM代码执行失败");
            }
        }
        
        // 默认返回值
        return @"Hotfix Applied";
    };
    
    // Hook方法
    BOOL success = [_methodInterceptor hookInstanceMethod:targetClass 
                                                 selector:selector 
                                                 callback:callback];
    
    return success;
}

- (BOOL)applyMethodHookPatch:(HotfixPatch *)patch 
                 targetClass:(Class)targetClass 
                    selector:(SEL)selector {
    
    NSLog(@"🪝 应用方法Hook补丁: %@.%@", patch.targetClass, patch.targetMethod);
    
    // 创建Hook回调
    HotfixInterceptorCallback callback = ^id(id target, SEL sel, NSArray *arguments) {
        NSLog(@"🎯 Hook拦截: %@.%@", NSStringFromClass([target class]), NSStringFromSelector(sel));
        
        // 执行前置逻辑
        if (patch.armCode) {
            [self.armBridge executeARMCode:patch.armCode withParams:arguments result:nil];
        }
        
        // 这里应该调用原始方法，简化处理
        NSLog(@"📞 调用原始方法");
        
        return nil;
    };
    
    BOOL success = [_methodInterceptor hookInstanceMethod:targetClass 
                                                 selector:selector 
                                                 callback:callback];
    
    return success;
}

- (BOOL)applyCodeInjectionPatch:(HotfixPatch *)patch 
                    targetClass:(Class)targetClass 
                       selector:(SEL)selector {
    
    NSLog(@"💉 应用代码注入补丁: %@.%@", patch.targetClass, patch.targetMethod);
    
    if (!patch.armCode) {
        NSLog(@"❌ 代码注入补丁缺少ARM代码");
        return NO;
    }
    
    // 加载ARM代码到内存
    CodeSegmentInfo *segment = [_codeLoader loadCode:patch.armCode 
                                           identifier:patch.patchId 
                                          permissions:CodePermissionRead | CodePermissionExecute];
    
    if (!segment) {
        NSLog(@"❌ ARM代码加载失败");
        return NO;
    }
    
    // 创建方法替换
    HotfixInterceptorCallback callback = ^id(id target, SEL sel, NSArray *arguments) {
        NSLog(@"💉 执行注入代码: %@", patch.patchId);
        
        id result = nil;
        BOOL success = [self.armBridge executeARMCode:patch.armCode withParams:arguments result:&result];
        
        return success ? result : nil;
    };
    
    BOOL success = [_methodInterceptor hookInstanceMethod:targetClass 
                                                 selector:selector 
                                                 callback:callback];
    
    return success;
}

- (BOOL)rollbackPatch:(NSString *)patchId {
    HotfixPatch *patch = _patches[patchId];
    if (!patch) {
        NSLog(@"❌ 补丁不存在: %@", patchId);
        return NO;
    }
    
    if (patch.status != HotfixPatchStatusApplied) {
        NSLog(@"⚠️ 补丁未应用，无需回滚: %@", patchId);
        return YES;
    }
    
    NSLog(@"🔄 回滚补丁: %@", patchId);
    
    // 恢复原始方法实现
    id originalImp = _originalImplementations[patchId];
    if (originalImp) {
        Class targetClass = NSClassFromString(patch.targetClass);
        SEL selector = NSSelectorFromString(patch.targetMethod);
        
        if (targetClass && selector) {
            [_methodInterceptor restoreMethod:targetClass 
                                     selector:selector 
                        originalImplementation:(IMP)originalImp];
        }
    }
    
    // 卸载代码段
    [_codeLoader unloadCode:patchId];
    
    // 更新状态
    patch.status = HotfixPatchStatusRolledBack;
    _appliedPatchCount--;
    
    NSLog(@"✅ 补丁回滚成功: %@", patchId);
    return YES;
}

- (HotfixPatch *)getPatch:(NSString *)patchId {
    return _patches[patchId];
}

- (NSArray<HotfixPatch *> *)getAllPatches {
    return [_patches.allValues copy];
}

- (NSArray<HotfixPatch *> *)getAppliedPatches {
    NSMutableArray *appliedPatches = [[NSMutableArray alloc] init];
    for (HotfixPatch *patch in _patches.allValues) {
        if (patch.status == HotfixPatchStatusApplied) {
            [appliedPatches addObject:patch];
        }
    }
    return [appliedPatches copy];
}

- (BOOL)isPatchApplied:(NSString *)patchId {
    HotfixPatch *patch = _patches[patchId];
    return patch && patch.status == HotfixPatchStatusApplied;
}

- (NSDictionary *)validatePatch:(HotfixPatch *)patch {
    NSMutableDictionary *result = [[NSMutableDictionary alloc] init];
    result[@"valid"] = @YES;
    result[@"warnings"] = [[NSMutableArray alloc] init];
    
    // 检查基本信息
    if (!patch.patchId || patch.patchId.length == 0) {
        result[@"valid"] = @NO;
        result[@"reason"] = @"补丁ID为空";
        return result;
    }
    
    if (!patch.targetClass || patch.targetClass.length == 0) {
        result[@"valid"] = @NO;
        result[@"reason"] = @"目标类为空";
        return result;
    }
    
    if (!patch.targetMethod || patch.targetMethod.length == 0) {
        result[@"valid"] = @NO;
        result[@"reason"] = @"目标方法为空";
        return result;
    }
    
    // 检查目标类是否存在
    Class targetClass = NSClassFromString(patch.targetClass);
    if (!targetClass) {
        result[@"valid"] = @NO;
        result[@"reason"] = [NSString stringWithFormat:@"目标类不存在: %@", patch.targetClass];
        return result;
    }
    
    // 检查目标方法是否存在
    SEL selector = NSSelectorFromString(patch.targetMethod);
    Method method = patch.isInstanceMethod ? 
        class_getInstanceMethod(targetClass, selector) :
        class_getClassMethod(targetClass, selector);
    
    if (!method) {
        [result[@"warnings"] addObject:[NSString stringWithFormat:@"目标方法不存在: %@", patch.targetMethod]];
    }
    
    // 检查ARM代码
    if (patch.armCode && patch.armCode.length > 0) {
        NSDictionary *codeValidation = [_codeLoader validateCodeSecurity:patch.armCode];
        if (![codeValidation[@"safe"] boolValue]) {
            result[@"valid"] = @NO;
            result[@"reason"] = [NSString stringWithFormat:@"ARM代码不安全: %@", codeValidation[@"reason"]];
            return result;
        }
    }
    
    return result;
}

- (NSArray<HotfixPatch *> *)checkPatchConflicts:(HotfixPatch *)patch {
    NSMutableArray *conflicts = [[NSMutableArray alloc] init];
    
    for (HotfixPatch *existingPatch in _patches.allValues) {
        if (existingPatch.status == HotfixPatchStatusApplied &&
            [existingPatch.targetClass isEqualToString:patch.targetClass] &&
            [existingPatch.targetMethod isEqualToString:patch.targetMethod]) {
            [conflicts addObject:existingPatch];
        }
    }
    
    return [conflicts copy];
}

- (BOOL)removePatch:(NSString *)patchId {
    HotfixPatch *patch = _patches[patchId];
    if (!patch) {
        return NO;
    }
    
    // 如果已应用，先回滚
    if (patch.status == HotfixPatchStatusApplied) {
        [self rollbackPatch:patchId];
    }
    
    // 移除补丁
    [_patches removeObjectForKey:patchId];
    [_originalImplementations removeObjectForKey:patchId];
    
    NSLog(@"✅ 补丁移除成功: %@", patchId);
    return YES;
}

- (void)clearAllPatches {
    // 回滚所有已应用的补丁
    NSArray *patchIds = [_patches.allKeys copy];
    for (NSString *patchId in patchIds) {
        [self rollbackPatch:patchId];
    }
    
    // 清理所有数据
    [_patches removeAllObjects];
    [_originalImplementations removeAllObjects];
    [_backups removeAllObjects];
    
    // 清理方法拦截器
    [_methodInterceptor clearAllHooks];
    
    // 清理代码加载器
    [_codeLoader clearAllSegments];
    
    _appliedPatchCount = 0;
    _failedPatchCount = 0;
    
    NSLog(@"✅ 清理所有补丁完成");
}

- (NSData *)exportPatchConfiguration {
    NSMutableArray *patchArray = [[NSMutableArray alloc] init];
    
    for (HotfixPatch *patch in _patches.allValues) {
        NSMutableDictionary *patchDict = [[NSMutableDictionary alloc] init];
        patchDict[@"patchId"] = patch.patchId;
        patchDict[@"version"] = patch.version;
        patchDict[@"name"] = patch.name;
        patchDict[@"description"] = patch.description;
        patchDict[@"targetClass"] = patch.targetClass;
        patchDict[@"targetMethod"] = patch.targetMethod;
        patchDict[@"isInstanceMethod"] = @(patch.isInstanceMethod);
        patchDict[@"methodSignature"] = patch.methodSignature;
        patchDict[@"status"] = @(patch.status);
        patchDict[@"type"] = @(patch.type);
        
        if (patch.armCode) {
            patchDict[@"armCode"] = [patch.armCode base64EncodedStringWithOptions:0];
        }
        
        if (patch.metadata) {
            patchDict[@"metadata"] = patch.metadata;
        }
        
        [patchArray addObject:patchDict];
    }
    
    NSDictionary *config = @{
        @"version": @"1.0",
        @"patches": patchArray,
        @"exportTime": [NSDate date].description
    };
    
    NSError *error;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:config options:NSJSONWritingPrettyPrinted error:&error];
    
    if (error) {
        NSLog(@"❌ 导出配置失败: %@", error.localizedDescription);
        return nil;
    }
    
    return jsonData;
}

- (BOOL)importPatchConfiguration:(NSData *)jsonData {
    NSError *error;
    NSDictionary *config = [NSJSONSerialization JSONObjectWithData:jsonData options:0 error:&error];
    
    if (error) {
        NSLog(@"❌ 导入配置失败: %@", error.localizedDescription);
        return NO;
    }
    
    NSArray *patchArray = config[@"patches"];
    if (!patchArray) {
        NSLog(@"❌ 配置中没有补丁数据");
        return NO;
    }
    
    NSUInteger successCount = 0;
    for (NSDictionary *patchDict in patchArray) {
        HotfixPatch *patch = [self createPatchFromDictionary:patchDict];
        if (patch) {
            _patches[patch.patchId] = patch;
            successCount++;
        }
    }
    
    NSLog(@"✅ 导入补丁配置成功: %lu/%lu", (unsigned long)successCount, (unsigned long)patchArray.count);
    return successCount > 0;
}

- (NSDictionary *)getHotfixStats {
    NSUInteger pendingCount = 0;
    NSUInteger appliedCount = 0;
    NSUInteger failedCount = 0;
    NSUInteger rolledBackCount = 0;
    
    for (HotfixPatch *patch in _patches.allValues) {
        switch (patch.status) {
            case HotfixPatchStatusPending:
                pendingCount++;
                break;
            case HotfixPatchStatusApplied:
                appliedCount++;
                break;
            case HotfixPatchStatusFailed:
                failedCount++;
                break;
            case HotfixPatchStatusRolledBack:
                rolledBackCount++;
                break;
        }
    }
    
    return @{
        @"totalPatches": @(_patches.count),
        @"pendingPatches": @(pendingCount),
        @"appliedPatches": @(appliedCount),
        @"failedPatches": @(failedCount),
        @"rolledBackPatches": @(rolledBackCount),
        @"hookedMethods": @([_methodInterceptor getAllHookedMethods].count),
        @"loadedCodeSegments": @([_codeLoader getAllLoadedSegments].count)
    };
}

- (void)setDebugMode:(BOOL)enabled {
    _debugMode = enabled;
    NSLog(@"🐛 热修复调试模式: %@", enabled ? @"启用" : @"禁用");
}

- (BOOL)setPatchEnabled:(NSString *)patchId enabled:(BOOL)enabled {
    HotfixPatch *patch = _patches[patchId];
    if (!patch) {
        return NO;
    }
    
    // 这里可以实现启用/禁用补丁的逻辑
    NSLog(@"🔧 补丁%@: %@", enabled ? @"启用" : @"禁用", patchId);
    return YES;
}

- (NSDictionary *)applyPatches:(NSArray<HotfixPatch *> *)patches {
    NSUInteger successCount = 0;
    NSUInteger failureCount = 0;
    NSMutableArray *errors = [[NSMutableArray alloc] init];
    
    for (HotfixPatch *patch in patches) {
        BOOL success = [self applyPatch:patch];
        if (success) {
            successCount++;
        } else {
            failureCount++;
            [errors addObject:[NSString stringWithFormat:@"补丁应用失败: %@", patch.patchId]];
        }
    }
    
    return @{
        @"total": @(patches.count),
        @"success": @(successCount),
        @"failure": @(failureCount),
        @"errors": errors
    };
}

- (NSDictionary *)rollbackPatches:(NSArray<NSString *> *)patchIds {
    NSUInteger successCount = 0;
    NSUInteger failureCount = 0;
    NSMutableArray *errors = [[NSMutableArray alloc] init];
    
    for (NSString *patchId in patchIds) {
        BOOL success = [self rollbackPatch:patchId];
        if (success) {
            successCount++;
        } else {
            failureCount++;
            [errors addObject:[NSString stringWithFormat:@"补丁回滚失败: %@", patchId]];
        }
    }
    
    return @{
        @"total": @(patchIds.count),
        @"success": @(successCount),
        @"failure": @(failureCount),
        @"errors": errors
    };
}

- (NSString *)createPatchBackup:(NSString *)patchId {
    HotfixPatch *patch = _patches[patchId];
    if (!patch) {
        return nil;
    }
    
    NSString *backupId = [NSString stringWithFormat:@"backup_%@_%@", patchId, @([[NSDate date] timeIntervalSince1970])];
    _backups[backupId] = patchId;
    
    NSLog(@"💾 创建补丁备份: %@ -> %@", patchId, backupId);
    return backupId;
}

- (BOOL)restoreFromBackup:(NSString *)backupId {
    NSString *patchId = _backups[backupId];
    if (!patchId) {
        NSLog(@"❌ 备份不存在: %@", backupId);
        return NO;
    }
    
    NSLog(@"📥 从备份恢复补丁: %@", patchId);
    return [self rollbackPatch:patchId];
}

@end
