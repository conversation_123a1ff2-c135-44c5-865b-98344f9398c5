//
//  ExecutionEnvironment.h
//  ArmRunDemo
//
//  Created by <PERSON> on 2025/7/4.
//  Copyright © 2025年 Ming Dai. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * CPU状态结构
 */
typedef struct {
    uint32_t registers[16];  // R0-R15寄存器
    uint32_t cpsr;          // 当前程序状态寄存器
    uint32_t spsr;          // 保存的程序状态寄存器
    uint64_t cycle_count;   // 周期计数
} CPUState;

/**
 * 内存访问类型
 */
typedef NS_ENUM(NSUInteger, MemoryAccessType) {
    MemoryAccessTypeRead,
    MemoryAccessTypeWrite,
    MemoryAccessTypeExecute
};

/**
 * 执行模式
 */
typedef NS_ENUM(NSUInteger, ExecutionMode) {
    ExecutionModeUser,       // 用户模式
    ExecutionModeSupervisor, // 管理模式
    ExecutionModeSystem,     // 系统模式
    ExecutionModeAbort,      // 中止模式
    ExecutionModeUndefined,  // 未定义模式
    ExecutionModeIRQ,        // IRQ模式
    ExecutionModeFIQ         // FIQ模式
};

/**
 * 内存访问回调
 */
typedef uint32_t (^MemoryReadCallback)(uint32_t address, NSUInteger size);
typedef void (^MemoryWriteCallback)(uint32_t address, uint32_t value, NSUInteger size);

/**
 * 系统调用回调
 */
typedef void (^SystemCallCallback)(uint32_t callNumber, CPUState *state);

/**
 * 异常处理回调
 */
typedef void (^ExceptionCallback)(NSString *exceptionType, uint32_t address, CPUState *state);

/**
 * 执行环境管理器
 * 管理虚拟CPU状态，提供内存访问接口和上下文切换
 */
@interface ExecutionEnvironment : NSObject

/**
 * 单例实例
 */
+ (instancetype)sharedInstance;

/**
 * 初始化执行环境
 * @return 是否初始化成功
 */
- (BOOL)initialize;

/**
 * 创建新的CPU上下文
 * @param identifier 上下文标识符
 * @return 是否创建成功
 */
- (BOOL)createContext:(NSString *)identifier;

/**
 * 切换到指定上下文
 * @param identifier 上下文标识符
 * @return 是否切换成功
 */
- (BOOL)switchToContext:(NSString *)identifier;

/**
 * 获取当前上下文标识符
 * @return 当前上下文标识符
 */
- (NSString * _Nullable)getCurrentContextIdentifier;

/**
 * 保存当前CPU状态
 * @param identifier 保存标识符
 * @return 是否保存成功
 */
- (BOOL)saveCPUState:(NSString *)identifier;

/**
 * 恢复CPU状态
 * @param identifier 保存标识符
 * @return 是否恢复成功
 */
- (BOOL)restoreCPUState:(NSString *)identifier;

/**
 * 获取CPU状态
 * @return 当前CPU状态
 */
- (CPUState)getCPUState;

/**
 * 设置CPU状态
 * @param state CPU状态
 */
- (void)setCPUState:(CPUState)state;

/**
 * 获取寄存器值
 * @param registerIndex 寄存器索引 (0-15)
 * @return 寄存器值
 */
- (uint32_t)getRegister:(NSUInteger)registerIndex;

/**
 * 设置寄存器值
 * @param registerIndex 寄存器索引 (0-15)
 * @param value 寄存器值
 */
- (void)setRegister:(NSUInteger)registerIndex value:(uint32_t)value;

/**
 * 获取CPSR寄存器
 * @return CPSR值
 */
- (uint32_t)getCPSR;

/**
 * 设置CPSR寄存器
 * @param cpsr CPSR值
 */
- (void)setCPSR:(uint32_t)cpsr;

/**
 * 获取当前执行模式
 * @return 执行模式
 */
- (ExecutionMode)getCurrentMode;

/**
 * 设置执行模式
 * @param mode 执行模式
 */
- (void)setExecutionMode:(ExecutionMode)mode;

/**
 * 读取内存
 * @param address 内存地址
 * @param size 读取大小 (1, 2, 4, 8字节)
 * @return 读取的值
 */
- (uint64_t)readMemory:(uint32_t)address size:(NSUInteger)size;

/**
 * 写入内存
 * @param address 内存地址
 * @param value 写入的值
 * @param size 写入大小 (1, 2, 4, 8字节)
 * @return 是否写入成功
 */
- (BOOL)writeMemory:(uint32_t)address value:(uint64_t)value size:(NSUInteger)size;

/**
 * 分配内存区域
 * @param size 内存大小
 * @param baseAddress 基地址 (0表示自动分配)
 * @return 分配的基地址，失败返回0
 */
- (uint32_t)allocateMemory:(size_t)size baseAddress:(uint32_t)baseAddress;

/**
 * 释放内存区域
 * @param baseAddress 基地址
 * @param size 内存大小
 * @return 是否释放成功
 */
- (BOOL)deallocateMemory:(uint32_t)baseAddress size:(size_t)size;

/**
 * 设置内存访问回调
 * @param readCallback 读取回调
 * @param writeCallback 写入回调
 */
- (void)setMemoryCallbacks:(MemoryReadCallback _Nullable)readCallback
             writeCallback:(MemoryWriteCallback _Nullable)writeCallback;

/**
 * 设置系统调用回调
 * @param callback 系统调用回调
 */
- (void)setSystemCallCallback:(SystemCallCallback _Nullable)callback;

/**
 * 设置异常处理回调
 * @param callback 异常处理回调
 */
- (void)setExceptionCallback:(ExceptionCallback _Nullable)callback;

/**
 * 触发系统调用
 * @param callNumber 调用号
 */
- (void)triggerSystemCall:(uint32_t)callNumber;

/**
 * 触发异常
 * @param exceptionType 异常类型
 * @param address 异常地址
 */
- (void)triggerException:(NSString *)exceptionType address:(uint32_t)address;

/**
 * 重置执行环境
 */
- (void)reset;

/**
 * 获取执行统计信息
 * @return 统计信息字典
 */
- (NSDictionary *)getExecutionStats;

/**
 * 设置调试模式
 * @param enabled 是否启用调试
 */
- (void)setDebugMode:(BOOL)enabled;

/**
 * 获取内存映射信息
 * @return 内存映射数组
 */
- (NSArray<NSDictionary *> *)getMemoryMap;

@end

NS_ASSUME_NONNULL_END
