//
//  ViewController.m
//  ArmRunDemo
//
//  Created by <PERSON> on 2025/7/4.
//

#import "ViewController.h"
#import "HotfixEngine/SimpleDynarmicBridge.h"

@interface ViewController ()
@property (nonatomic, strong) SimpleDynarmicBridge *dynarmicBridge;
@property (nonatomic, weak) IBOutlet UILabel *statusLabel;
@property (nonatomic, weak) IBOutlet UIButton *testButton;
@property (nonatomic, weak) IBOutlet UITextView *logTextView;
@end

@implementation ViewController

- (void)viewDidLoad {
    [super viewDidLoad];

    [self setupUI];
    [self initializeDynarmic];
}

- (void)setupUI {
    self.view.backgroundColor = [UIColor systemBackgroundColor];

    // 状态标签
    UILabel *statusLabel = [[UILabel alloc] init];
    statusLabel.text = @"Dynarmic状态: 未初始化";
    statusLabel.textAlignment = NSTextAlignmentCenter;
    statusLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:statusLabel];
    self.statusLabel = statusLabel;

    // 测试按钮
    UIButton *testButton = [UIButton buttonWithType:UIButtonTypeSystem];
    [testButton setTitle:@"测试ARM代码执行" forState:UIControlStateNormal];
    [testButton addTarget:self action:@selector(testARMExecution:) forControlEvents:UIControlEventTouchUpInside];
    testButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:testButton];
    self.testButton = testButton;

    // 日志文本视图
    UITextView *logTextView = [[UITextView alloc] init];
    logTextView.backgroundColor = [UIColor systemGray6Color];
    logTextView.font = [UIFont fontWithName:@"Menlo" size:12];
    logTextView.editable = NO;
    logTextView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:logTextView];
    self.logTextView = logTextView;

    // 设置约束
    [NSLayoutConstraint activateConstraints:@[
        [statusLabel.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor constant:20],
        [statusLabel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:20],
        [statusLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-20],

        [testButton.topAnchor constraintEqualToAnchor:statusLabel.bottomAnchor constant:20],
        [testButton.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],

        [logTextView.topAnchor constraintEqualToAnchor:testButton.bottomAnchor constant:20],
        [logTextView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:20],
        [logTextView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-20],
        [logTextView.bottomAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.bottomAnchor constant:-20]
    ]];
}

- (void)initializeDynarmic {
    [self addLog:@"开始初始化简化版ARM执行引擎..."];

    self.dynarmicBridge = [SimpleDynarmicBridge sharedInstance];
    BOOL success = [self.dynarmicBridge initializeEngine];

    if (success) {
        self.statusLabel.text = @"ARM引擎状态: 已初始化";
        self.statusLabel.textColor = [UIColor systemGreenColor];
        [self addLog:@"✅ ARM执行引擎初始化成功"];
        self.testButton.enabled = YES;
    } else {
        self.statusLabel.text = @"ARM引擎状态: 初始化失败";
        self.statusLabel.textColor = [UIColor systemRedColor];
        [self addLog:@"❌ ARM执行引擎初始化失败"];
        self.testButton.enabled = NO;
    }
}

- (IBAction)testARMExecution:(id)sender {
    [self addLog:@"\n开始测试ARM代码执行..."];

    // 简单的ARM代码: lsls r0, r1, #2 (r0 = r1 << 2)
    // 机器码: 0x0088 (Thumb模式)
    uint16_t armCode[] = {0x0088, 0xE7FE}; // lsls r0, r1, #2; b +#0 (infinite loop)
    NSData *codeData = [NSData dataWithBytes:armCode length:sizeof(armCode)];

    // 设置参数: r1 = 2
    NSArray *params = @[@1, @2]; // r0=1, r1=2

    id result = nil;
    BOOL success = [self.dynarmicBridge executeARMCode:codeData withParams:params result:&result];

    if (success) {
        [self addLog:[NSString stringWithFormat:@"✅ ARM代码执行成功"]];
        [self addLog:[NSString stringWithFormat:@"   输入: r1 = 2"]];
        [self addLog:[NSString stringWithFormat:@"   操作: r0 = r1 << 2"]];
        [self addLog:[NSString stringWithFormat:@"   结果: r0 = %@", result]];
        [self addLog:[NSString stringWithFormat:@"   期望: r0 = 8"]];

        if ([result intValue] == 8) {
            [self addLog:@"🎉 测试通过！"];
        } else {
            [self addLog:@"⚠️ 结果不符合预期"];
        }
    } else {
        [self addLog:@"❌ ARM代码执行失败"];
    }

    // 显示寄存器状态
    [self addLog:@"\n寄存器状态:"];
    for (int i = 0; i < 4; i++) {
        uint32_t regValue = [self.dynarmicBridge getRegister:i];
        [self addLog:[NSString stringWithFormat:@"   r%d = 0x%08X (%u)", i, regValue, regValue]];
    }

    uint32_t cpsr = [self.dynarmicBridge getCPSR];
    [self addLog:[NSString stringWithFormat:@"   CPSR = 0x%08X", cpsr]];
}

- (void)addLog:(NSString *)message {
    dispatch_async(dispatch_get_main_queue(), ^{
        NSString *timestamp = [NSDateFormatter localizedStringFromDate:[NSDate date]
                                                             dateStyle:NSDateFormatterNoStyle
                                                             timeStyle:NSDateFormatterMediumStyle];
        NSString *logEntry = [NSString stringWithFormat:@"[%@] %@\n", timestamp, message];

        self.logTextView.text = [self.logTextView.text stringByAppendingString:logEntry];

        // 滚动到底部
        if (self.logTextView.text.length > 0) {
            NSRange bottom = NSMakeRange(self.logTextView.text.length - 1, 1);
            [self.logTextView scrollRangeToVisible:bottom];
        }
    });
}

@end
