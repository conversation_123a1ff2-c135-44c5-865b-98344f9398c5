//
//  ViewController.m
//  ArmRunDemo
//
//  Created by <PERSON> on 2025/7/4.
//

#import "ViewController.h"
#import "HotfixEngine/SimpleDynarmicBridge.h"
#import "HotfixEngine/HotfixManager.h"
#import "HotfixEngine/HotfixTestSuite.h"

@interface ViewController ()
@property (nonatomic, strong) SimpleDynarmicBridge *dynarmicBridge;
@property (nonatomic, strong) HotfixManager *hotfixManager;
@property (nonatomic, weak) IBOutlet UILabel *statusLabel;
@property (nonatomic, weak) IBOutlet UIButton *testButton;
@property (nonatomic, weak) IBOutlet UIButton *hotfixButton;
@property (nonatomic, weak) IBOutlet UIButton *demoButton;
@property (nonatomic, weak) IBOutlet UIButton *testSuiteButton;
@property (nonatomic, weak) IBOutlet UITextView *logTextView;
@end

@implementation ViewController

- (void)viewDidLoad {
    [super viewDidLoad];

    [self setupUI];
    [self initializeDynarmic];
    [self initializeHotfix];
}

- (void)setupUI {
    self.view.backgroundColor = [UIColor systemBackgroundColor];

    // 状态标签
    UILabel *statusLabel = [[UILabel alloc] init];
    statusLabel.text = @"ARM引擎状态: 未初始化";
    statusLabel.textAlignment = NSTextAlignmentCenter;
    statusLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:statusLabel];
    self.statusLabel = statusLabel;

    // 测试按钮
    UIButton *testButton = [UIButton buttonWithType:UIButtonTypeSystem];
    [testButton setTitle:@"测试ARM代码执行" forState:UIControlStateNormal];
    [testButton addTarget:self action:@selector(testARMExecution:) forControlEvents:UIControlEventTouchUpInside];
    testButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:testButton];
    self.testButton = testButton;

    // 热修复按钮
    UIButton *hotfixButton = [UIButton buttonWithType:UIButtonTypeSystem];
    [hotfixButton setTitle:@"测试热修复功能" forState:UIControlStateNormal];
    [hotfixButton addTarget:self action:@selector(testHotfix:) forControlEvents:UIControlEventTouchUpInside];
    hotfixButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:hotfixButton];
    self.hotfixButton = hotfixButton;

    // 演示按钮
    UIButton *demoButton = [UIButton buttonWithType:UIButtonTypeSystem];
    [demoButton setTitle:@"演示方法替换" forState:UIControlStateNormal];
    [demoButton addTarget:self action:@selector(testMethodDemo:) forControlEvents:UIControlEventTouchUpInside];
    demoButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:demoButton];
    self.demoButton = demoButton;

    // 测试套件按钮
    UIButton *testSuiteButton = [UIButton buttonWithType:UIButtonTypeSystem];
    [testSuiteButton setTitle:@"运行完整测试" forState:UIControlStateNormal];
    [testSuiteButton addTarget:self action:@selector(runTestSuite:) forControlEvents:UIControlEventTouchUpInside];
    testSuiteButton.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:testSuiteButton];
    self.testSuiteButton = testSuiteButton;

    // 日志文本视图
    UITextView *logTextView = [[UITextView alloc] init];
    logTextView.backgroundColor = [UIColor systemGray6Color];
    logTextView.font = [UIFont fontWithName:@"Menlo" size:12];
    logTextView.editable = NO;
    logTextView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:logTextView];
    self.logTextView = logTextView;

    // 设置约束
    [NSLayoutConstraint activateConstraints:@[
        [statusLabel.topAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.topAnchor constant:20],
        [statusLabel.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:20],
        [statusLabel.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-20],

        [testButton.topAnchor constraintEqualToAnchor:statusLabel.bottomAnchor constant:20],
        [testButton.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],

        [hotfixButton.topAnchor constraintEqualToAnchor:testButton.bottomAnchor constant:10],
        [hotfixButton.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],

        [demoButton.topAnchor constraintEqualToAnchor:hotfixButton.bottomAnchor constant:10],
        [demoButton.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],

        [testSuiteButton.topAnchor constraintEqualToAnchor:demoButton.bottomAnchor constant:10],
        [testSuiteButton.centerXAnchor constraintEqualToAnchor:self.view.centerXAnchor],

        [logTextView.topAnchor constraintEqualToAnchor:testSuiteButton.bottomAnchor constant:20],
        [logTextView.leadingAnchor constraintEqualToAnchor:self.view.leadingAnchor constant:20],
        [logTextView.trailingAnchor constraintEqualToAnchor:self.view.trailingAnchor constant:-20],
        [logTextView.bottomAnchor constraintEqualToAnchor:self.view.safeAreaLayoutGuide.bottomAnchor constant:-20]
    ]];
}

- (void)initializeDynarmic {
    [self addLog:@"开始初始化简化版ARM执行引擎..."];

    self.dynarmicBridge = [SimpleDynarmicBridge sharedInstance];
    BOOL success = [self.dynarmicBridge initializeEngine];

    if (success) {
        self.statusLabel.text = @"ARM引擎状态: 已初始化";
        self.statusLabel.textColor = [UIColor systemGreenColor];
        [self addLog:@"✅ ARM执行引擎初始化成功"];
        self.testButton.enabled = YES;
    } else {
        self.statusLabel.text = @"ARM引擎状态: 初始化失败";
        self.statusLabel.textColor = [UIColor systemRedColor];
        [self addLog:@"❌ ARM执行引擎初始化失败"];
        self.testButton.enabled = NO;
    }
}

- (void)initializeHotfix {
    [self addLog:@"开始初始化热修复管理器..."];

    self.hotfixManager = [HotfixManager sharedInstance];
    BOOL success = [self.hotfixManager initialize];

    if (success) {
        [self addLog:@"✅ 热修复管理器初始化成功"];
        self.hotfixButton.enabled = YES;
        self.demoButton.enabled = YES;
        self.testSuiteButton.enabled = YES;
    } else {
        [self addLog:@"❌ 热修复管理器初始化失败"];
        self.hotfixButton.enabled = NO;
        self.demoButton.enabled = NO;
        self.testSuiteButton.enabled = NO;
    }
}

- (IBAction)testARMExecution:(id)sender {
    [self addLog:@"\n=== 开始ARM代码执行测试 ==="];

    // 测试1: lsls r0, r1, #2 (r0 = r1 << 2)
    [self testLSLSInstruction];

    // 测试2: mov r0, #42
    [self testMOVInstruction];

    // 测试3: add r0, r1, r2
    [self testADDInstruction];

    [self addLog:@"=== ARM代码执行测试完成 ===\n"];
}

- (void)testLSLSInstruction {
    [self addLog:@"\n--- 测试1: LSLS指令 ---"];

    // 机器码: 0x0088 (lsls r0, r1, #2)
    uint16_t armCode[] = {0x0088};
    NSData *codeData = [NSData dataWithBytes:armCode length:sizeof(armCode)];

    // 设置参数: r1 = 2
    NSArray *params = @[@0, @2]; // r0=0, r1=2

    id result = nil;
    BOOL success = [self.dynarmicBridge executeARMCode:codeData withParams:params result:&result];

    if (success) {
        [self addLog:@"✅ LSLS指令执行成功"];
        [self addLog:[NSString stringWithFormat:@"   输入: r1 = 2"]];
        [self addLog:[NSString stringWithFormat:@"   操作: lsls r0, r1, #2"]];
        [self addLog:[NSString stringWithFormat:@"   结果: r0 = %@", result]];
        [self addLog:[NSString stringWithFormat:@"   期望: r0 = 8"]];

        if ([result intValue] == 8) {
            [self addLog:@"🎉 LSLS测试通过！"];
        } else {
            [self addLog:@"⚠️ LSLS结果不符合预期"];
        }
    } else {
        [self addLog:@"❌ LSLS指令执行失败"];
    }

    [self displayRegisterState];
}

- (void)testMOVInstruction {
    [self addLog:@"\n--- 测试2: MOV指令 ---"];

    // 重置寄存器
    [self.dynarmicBridge reset];

    // 机器码: 0x202A (mov r0, #42)
    uint16_t armCode[] = {0x202A};
    NSData *codeData = [NSData dataWithBytes:armCode length:sizeof(armCode)];

    NSArray *params = @[]; // 无参数

    id result = nil;
    BOOL success = [self.dynarmicBridge executeARMCode:codeData withParams:params result:&result];

    if (success) {
        [self addLog:@"✅ MOV指令执行成功"];
        [self addLog:[NSString stringWithFormat:@"   操作: mov r0, #42"]];
        [self addLog:[NSString stringWithFormat:@"   结果: r0 = %@", result]];
        [self addLog:[NSString stringWithFormat:@"   期望: r0 = 42"]];

        if ([result intValue] == 42) {
            [self addLog:@"🎉 MOV测试通过！"];
        } else {
            [self addLog:@"⚠️ MOV结果不符合预期"];
        }
    } else {
        [self addLog:@"❌ MOV指令执行失败"];
    }

    [self displayRegisterState];
}

- (void)testADDInstruction {
    [self addLog:@"\n--- 测试3: ADD指令 ---"];

    // 重置寄存器
    [self.dynarmicBridge reset];

    // 机器码: 0x1888 (add r0, r1, r2)
    uint16_t armCode[] = {0x1888};
    NSData *codeData = [NSData dataWithBytes:armCode length:sizeof(armCode)];

    // 设置参数: r1 = 10, r2 = 20
    NSArray *params = @[@0, @10, @20]; // r0=0, r1=10, r2=20

    id result = nil;
    BOOL success = [self.dynarmicBridge executeARMCode:codeData withParams:params result:&result];

    if (success) {
        [self addLog:@"✅ ADD指令执行成功"];
        [self addLog:[NSString stringWithFormat:@"   输入: r1 = 10, r2 = 20"]];
        [self addLog:[NSString stringWithFormat:@"   操作: add r0, r1, r2"]];
        [self addLog:[NSString stringWithFormat:@"   结果: r0 = %@", result]];
        [self addLog:[NSString stringWithFormat:@"   期望: r0 = 30"]];

        if ([result intValue] == 30) {
            [self addLog:@"🎉 ADD测试通过！"];
        } else {
            [self addLog:@"⚠️ ADD结果不符合预期"];
        }
    } else {
        [self addLog:@"❌ ADD指令执行失败"];
    }

    [self displayRegisterState];
}

- (void)displayRegisterState {
    [self addLog:@"   寄存器状态:"];
    for (int i = 0; i < 4; i++) {
        uint32_t regValue = [self.dynarmicBridge getRegister:i];
        [self addLog:[NSString stringWithFormat:@"     r%d = 0x%08X (%u)", i, regValue, regValue]];
    }

    uint32_t cpsr = [self.dynarmicBridge getCPSR];
    [self addLog:[NSString stringWithFormat:@"     CPSR = 0x%08X", cpsr]];
}

- (void)addLog:(NSString *)message {
    dispatch_async(dispatch_get_main_queue(), ^{
        NSString *timestamp = [NSDateFormatter localizedStringFromDate:[NSDate date]
                                                             dateStyle:NSDateFormatterNoStyle
                                                             timeStyle:NSDateFormatterMediumStyle];
        NSString *logEntry = [NSString stringWithFormat:@"[%@] %@\n", timestamp, message];

        self.logTextView.text = [self.logTextView.text stringByAppendingString:logEntry];

        // 滚动到底部
        if (self.logTextView.text.length > 0) {
            NSRange bottom = NSMakeRange(self.logTextView.text.length - 1, 1);
            [self.logTextView scrollRangeToVisible:bottom];
        }
    });
}

#pragma mark - 热修复测试方法

- (IBAction)testHotfix:(id)sender {
    [self addLog:@"\n=== 开始热修复功能测试 ==="];

    // 创建测试补丁
    [self createAndApplyTestPatch];

    // 显示热修复统计
    [self displayHotfixStats];

    [self addLog:@"=== 热修复功能测试完成 ===\n"];
}

- (void)createAndApplyTestPatch {
    [self addLog:@"\n--- 创建测试补丁 ---"];

    // 创建补丁JSON数据
    NSDictionary *patchDict = @{
        @"patchId": @"test_patch_001",
        @"version": @"1.0.0",
        @"name": @"测试补丁",
        @"description": @"用于演示热修复功能的测试补丁",
        @"type": @"methodReplace",
        @"targetClass": @"ViewController",
        @"targetMethod": @"getDemoMessage",
        @"isInstanceMethod": @YES,
        @"methodSignature": @"@@:",
        @"armCode": @"", // 空ARM代码，使用默认实现
        @"metadata": @{
            @"author": @"HotfixDemo",
            @"priority": @"high"
        }
    };

    NSError *error;
    NSData *patchData = [NSJSONSerialization dataWithJSONObject:patchDict options:0 error:&error];

    if (error) {
        [self addLog:[NSString stringWithFormat:@"❌ 补丁JSON创建失败: %@", error.localizedDescription]];
        return;
    }

    // 加载补丁
    HotfixPatch *patch = [self.hotfixManager loadPatchFromJSON:patchData];
    if (!patch) {
        [self addLog:@"❌ 补丁加载失败"];
        return;
    }

    [self addLog:[NSString stringWithFormat:@"✅ 补丁加载成功: %@", patch.patchId]];

    // 应用补丁
    BOOL success = [self.hotfixManager applyPatch:patch];
    if (success) {
        [self addLog:[NSString stringWithFormat:@"✅ 补丁应用成功: %@", patch.patchId]];
    } else {
        [self addLog:[NSString stringWithFormat:@"❌ 补丁应用失败: %@", patch.patchId]];
    }
}

- (void)displayHotfixStats {
    [self addLog:@"\n--- 热修复统计信息 ---"];

    NSDictionary *stats = [self.hotfixManager getHotfixStats];
    [self addLog:[NSString stringWithFormat:@"   总补丁数: %@", stats[@"totalPatches"]]];
    [self addLog:[NSString stringWithFormat:@"   已应用: %@", stats[@"appliedPatches"]]];
    [self addLog:[NSString stringWithFormat:@"   待应用: %@", stats[@"pendingPatches"]]];
    [self addLog:[NSString stringWithFormat:@"   失败: %@", stats[@"failedPatches"]]];
    [self addLog:[NSString stringWithFormat:@"   已回滚: %@", stats[@"rolledBackPatches"]]];
    [self addLog:[NSString stringWithFormat:@"   Hook方法数: %@", stats[@"hookedMethods"]]];
    [self addLog:[NSString stringWithFormat:@"   代码段数: %@", stats[@"loadedCodeSegments"]]];
}

- (IBAction)testMethodDemo:(id)sender {
    [self addLog:@"\n=== 开始方法替换演示 ==="];

    // 调用原始方法
    [self addLog:@"调用原始方法:"];
    NSString *originalMessage = [self getDemoMessage];
    [self addLog:[NSString stringWithFormat:@"   结果: %@", originalMessage]];

    // 创建并应用方法替换补丁
    [self createMethodReplacePatch];

    // 再次调用方法（应该被热修复替换）
    [self addLog:@"\n应用热修复后调用方法:"];
    NSString *patchedMessage = [self getDemoMessage];
    [self addLog:[NSString stringWithFormat:@"   结果: %@", patchedMessage]];

    // 显示对比
    if (![originalMessage isEqualToString:patchedMessage]) {
        [self addLog:@"🎉 方法替换成功！行为已改变"];
    } else {
        [self addLog:@"⚠️ 方法替换可能未生效"];
    }

    [self addLog:@"=== 方法替换演示完成 ===\n"];
}

- (void)createMethodReplacePatch {
    [self addLog:@"创建方法替换补丁..."];

    NSDictionary *patchDict = @{
        @"patchId": @"method_replace_demo",
        @"version": @"1.0.0",
        @"name": @"方法替换演示",
        @"description": @"演示如何替换getDemoMessage方法",
        @"type": @"methodReplace",
        @"targetClass": @"ViewController",
        @"targetMethod": @"getDemoMessage",
        @"isInstanceMethod": @YES,
        @"methodSignature": @"@@:",
        @"armCode": @"", // 使用默认回调实现
        @"metadata": @{
            @"demo": @"methodReplace"
        }
    };

    NSData *patchData = [NSJSONSerialization dataWithJSONObject:patchDict options:0 error:nil];
    HotfixPatch *patch = [self.hotfixManager loadPatchFromJSON:patchData];

    if (patch) {
        BOOL success = [self.hotfixManager applyPatch:patch];
        [self addLog:[NSString stringWithFormat:@"%@ 方法替换补丁", success ? @"✅" : @"❌"]];
    }
}

// 演示用的原始方法
- (NSString *)getDemoMessage {
    return @"这是原始方法的返回值";
}

#pragma mark - 测试套件

- (IBAction)runTestSuite:(id)sender {
    [self addLog:@"\n🧪 开始运行完整测试套件..."];

    HotfixTestSuite *testSuite = [HotfixTestSuite sharedInstance];

    // 设置进度回调
    __weak typeof(self) weakSelf = self;
    [testSuite setTestProgressCallback:^(NSString *testName, BOOL completed) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (!completed) {
                [weakSelf addLog:[NSString stringWithFormat:@"🔄 开始测试: %@", testName]];
            }
        });
    }];

    // 在后台线程运行测试
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        NSArray<HotfixTestResult *> *results = [testSuite runAllTests];

        dispatch_async(dispatch_get_main_queue(), ^{
            [weakSelf displayTestResults:results];
        });
    });
}

- (void)displayTestResults:(NSArray<HotfixTestResult *> *)results {
    [self addLog:@"\n📊 测试结果汇总:"];

    NSDictionary *stats = [[HotfixTestSuite sharedInstance] getTestStatistics:results];
    [self addLog:[NSString stringWithFormat:@"   总测试数: %@", stats[@"total"]]];
    [self addLog:[NSString stringWithFormat:@"   通过: %@", stats[@"passed"]]];
    [self addLog:[NSString stringWithFormat:@"   失败: %@", stats[@"failed"]]];
    [self addLog:[NSString stringWithFormat:@"   成功率: %.1f%%", [stats[@"successRate"] doubleValue]]];
    [self addLog:[NSString stringWithFormat:@"   总耗时: %.3fs", [stats[@"totalDuration"] doubleValue]]];

    [self addLog:@"\n📋 详细结果:"];
    for (HotfixTestResult *result in results) {
        NSString *status = result.passed ? @"✅" : @"❌";
        [self addLog:[NSString stringWithFormat:@"   %@ %@ (%.3fs)", status, result.testName, result.duration]];
        [self addLog:[NSString stringWithFormat:@"      %@", result.message]];
    }

    // 生成完整报告
    NSString *report = [[HotfixTestSuite sharedInstance] generateTestReport:results];
    NSLog(@"\n%@", report);

    [self addLog:@"\n🧪 测试套件运行完成"];

    // 显示总体结果
    NSInteger passedCount = [stats[@"passed"] integerValue];
    NSInteger totalCount = [stats[@"total"] integerValue];

    if (passedCount == totalCount) {
        [self addLog:@"🎉 所有测试通过！热修复功能正常"];
    } else {
        [self addLog:[NSString stringWithFormat:@"⚠️ %ld个测试失败，请检查相关功能", (long)(totalCount - passedCount)]];
    }
}

@end
