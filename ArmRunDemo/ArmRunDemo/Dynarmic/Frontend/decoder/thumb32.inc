// Load/Store Multiple
//INST(thumb32_SRS_1,          "SRS",                      "1110100000-0--------------------")
//INST(thumb32_RFE_2,          "RFE",                      "1110100000-1--------------------")
INST(thumb32_STMIA,          "STMIA/STMEA",              "1110100010W0nnnn0iiiiiiiiiiiiiii")
INST(thumb32_POP,            "POP",                      "1110100010111101iiiiiiiiiiiiiiii")
INST(thumb32_LDMIA,          "LDMIA/LDMFD",              "1110100010W1nnnniiiiiiiiiiiiiiii")
INST(thumb32_PUSH,           "PUSH",                     "11101001001011010iiiiiiiiiiiiiii")
INST(thumb32_STMDB,          "STMDB/STMFD",              "1110100100W0nnnn0iiiiiiiiiiiiiii")
INST(thumb32_LDMDB,          "LDMDB/LDMEA",              "1110100100W1nnnniiiiiiiiiiiiiiii")
//INST(thumb32_SRS_1,          "SRS",                      "1110100110-0--------------------")
//INST(thumb32_RFE_2,          "RFE",                      "1110100110-1--------------------")

// Load/Store Dual, Load/Store Exclusive, Table Branch
INST(thumb32_STREX,          "STREX",                    "111010000100nnnnttttddddiiiiiiii")
INST(thumb32_LDREX,          "LDREX",                    "111010000101nnnntttt1111iiiiiiii")
INST(thumb32_STRD_imm_1,     "STRD (imm)",               "11101000U110nnnnttttssssiiiiiiii")
INST(thumb32_STRD_imm_2,     "STRD (imm)",               "11101001U1W0nnnnttttssssiiiiiiii")
INST(thumb32_LDRD_lit_1,     "LDRD (lit)",               "11101000U1111111ttttssssiiiiiiii")
INST(thumb32_LDRD_lit_2,     "LDRD (lit)",               "11101001U1W11111ttttssssiiiiiiii")
INST(thumb32_LDRD_imm_1,     "LDRD (imm)",               "11101000U111nnnnttttssssiiiiiiii")
INST(thumb32_LDRD_imm_2,     "LDRD (imm)",               "11101001U1W1nnnnttttssssiiiiiiii")
INST(thumb32_STL,            "STL",                      "111010001100nnnntttt111110101111") // v8
INST(thumb32_LDA,            "LDA",                      "111010001101nnnntttt111110101111") // v8
INST(thumb32_STREXB,         "STREXB",                   "111010001100nnnntttt11110100dddd")
INST(thumb32_STREXH,         "STREXH",                   "111010001100nnnntttt11110101dddd")
INST(thumb32_STREXD,         "STREXD",                   "111010001100nnnnttttuuuu0111dddd")
INST(thumb32_TBB,            "TBB",                      "111010001101nnnn111100000000mmmm")
INST(thumb32_TBH,            "TBH",                      "111010001101nnnn111100000001mmmm")
INST(thumb32_LDREXB,         "LDREXB",                   "111010001101nnnntttt111101001111")
INST(thumb32_LDREXH,         "LDREXH",                   "111010001101nnnntttt111101011111")
INST(thumb32_LDREXD,         "LDREXD",                   "111010001101nnnnttttuuuu01111111")

// Data Processing (Shifted Register)
INST(thumb32_TST_reg,        "TST (reg)",                "111010100001nnnn0vvv1111vvttmmmm")
INST(thumb32_AND_reg,        "AND (reg)",                "11101010000Snnnn0vvvddddvvttmmmm")
INST(thumb32_BIC_reg,        "BIC (reg)",                "11101010001Snnnn0vvvddddvvttmmmm")
INST(thumb32_MOV_reg,        "MOV (reg)",                "11101010010S11110vvvddddvvttmmmm")
INST(thumb32_ORR_reg,        "ORR (reg)",                "11101010010Snnnn0vvvddddvvttmmmm")
INST(thumb32_MVN_reg,        "MVN (reg)",                "11101010011S11110vvvddddvvttmmmm")
INST(thumb32_ORN_reg,        "ORN (reg)",                "11101010011Snnnn0vvvddddvvttmmmm")
INST(thumb32_TEQ_reg,        "TEQ (reg)",                "111010101001nnnn0vvv1111vvttmmmm")
INST(thumb32_EOR_reg,        "EOR (reg)",                "11101010100Snnnn0vvvddddvvttmmmm")
INST(thumb32_PKH,            "PKH",                      "111010101100nnnn0vvvddddvvt0mmmm")
INST(thumb32_CMN_reg,        "CMN (reg)",                "111010110001nnnn0vvv1111vvttmmmm")
INST(thumb32_ADD_reg,        "ADD (reg)",                "11101011000Snnnn0vvvddddvvttmmmm")
INST(thumb32_ADC_reg,        "ADC (reg)",                "11101011010Snnnn0vvvddddvvttmmmm")
INST(thumb32_SBC_reg,        "SBC (reg)",                "11101011011Snnnn0vvvddddvvttmmmm")
INST(thumb32_CMP_reg,        "CMP (reg)",                "111010111011nnnn0vvv1111vvttmmmm")
INST(thumb32_SUB_reg,        "SUB (reg)",                "11101011101Snnnn0vvvddddvvttmmmm")
INST(thumb32_RSB_reg,        "RSB (reg)",                "11101011110Snnnn0vvvddddvvttmmmm")

// Data Processing (Modified Immediate)
INST(thumb32_TST_imm,        "TST (imm)",                "11110v000001nnnn0vvv1111vvvvvvvv")
INST(thumb32_AND_imm,        "AND (imm)",                "11110v00000Snnnn0vvvddddvvvvvvvv")
INST(thumb32_BIC_imm,        "BIC (imm)",                "11110v00001Snnnn0vvvddddvvvvvvvv")
INST(thumb32_MOV_imm,        "MOV (imm)",                "11110v00010S11110vvvddddvvvvvvvv")
INST(thumb32_ORR_imm,        "ORR (imm)",                "11110v00010Snnnn0vvvddddvvvvvvvv")
INST(thumb32_MVN_imm,        "MVN (imm)",                "11110v00011S11110vvvddddvvvvvvvv")
INST(thumb32_ORN_imm,        "ORN (imm)",                "11110v00011Snnnn0vvvddddvvvvvvvv")
INST(thumb32_TEQ_imm,        "TEQ (imm)",                "11110v001001nnnn0vvv1111vvvvvvvv")
INST(thumb32_EOR_imm,        "EOR (imm)",                "11110v00100Snnnn0vvvddddvvvvvvvv")
INST(thumb32_CMN_imm,        "CMN (imm)",                "11110v010001nnnn0vvv1111vvvvvvvv")
INST(thumb32_ADD_imm_1,      "ADD (imm)",                "11110v01000Snnnn0vvvddddvvvvvvvv")
INST(thumb32_ADC_imm,        "ADC (imm)",                "11110v01010Snnnn0vvvddddvvvvvvvv")
INST(thumb32_SBC_imm,        "SBC (imm)",                "11110v01011Snnnn0vvvddddvvvvvvvv")
INST(thumb32_CMP_imm,        "CMP (imm)",                "11110v011011nnnn0vvv1111vvvvvvvv")
INST(thumb32_SUB_imm_1,      "SUB (imm)",                "11110v01101Snnnn0vvvddddvvvvvvvv")
INST(thumb32_RSB_imm,        "RSB (imm)",                "11110v01110Snnnn0vvvddddvvvvvvvv")

// Data Processing (Plain Binary Immediate)
INST(thumb32_ADR_t3,         "ADR",                      "11110i10000011110iiiddddiiiiiiii")
INST(thumb32_ADD_imm_2,      "ADD (imm)",                "11110i100000nnnn0iiiddddiiiiiiii")
INST(thumb32_MOVW_imm,       "MOVW (imm)",               "11110i100100iiii0iiiddddiiiiiiii")
INST(thumb32_ADR_t2,         "ADR",                      "11110i10101011110iiiddddiiiiiiii")
INST(thumb32_SUB_imm_2,      "SUB (imm)",                "11110i101010nnnn0iiiddddiiiiiiii")
INST(thumb32_MOVT,           "MOVT",                     "11110i101100iiii0iiiddddiiiiiiii")
INST(thumb32_UDF,            "Invalid decoding",         "11110011-010----0000----0001----")
INST(thumb32_SSAT16,         "SSAT16",                   "111100110010nnnn0000dddd0000iiii")
INST(thumb32_USAT16,         "USAT16",                   "************nnnn0000dddd0000iiii")
INST(thumb32_SSAT,           "SSAT",                     "1111001100s0nnnn0iiiddddii0bbbbb")
INST(thumb32_USAT,           "USAT",                     "1111001110s0nnnn0iiiddddii0bbbbb")
INST(thumb32_SBFX,           "SBFX",                     "111100110100nnnn0iiiddddii0wwwww")
INST(thumb32_BFC,            "BFC",                      "11110011011011110iiiddddii0bbbbb")
INST(thumb32_BFI,            "BFI",                      "111100110110nnnn0iiiddddii0bbbbb")
INST(thumb32_UBFX,           "UBFX",                     "111100111100nnnn0iiiddddii0wwwww")

// Branches and Miscellaneous Control
//INST(thumb32_MSR_banked,     "MSR (banked)",             "***********-----10-0------1-----")
INST(thumb32_MSR_reg,        "MSR (reg)",                "***********Rnnnn1000mmmm00000000")

INST(thumb32_NOP,            "NOP",                      "************1111****************")
INST(thumb32_YIELD,          "YIELD",                    "************11111000000000000001")
INST(thumb32_WFE,            "WFE",                      "************11111000000000000010")
INST(thumb32_WFI,            "WFI",                      "************11111000000000000011")
INST(thumb32_SEV,            "SEV",                      "************11111000000000000100")
INST(thumb32_SEVL,           "SEVL",                     "************11111000000000000101")
//INST(thumb32_DBG,            "DBG",                      "************----10-0-0001111----")
//INST(thumb32_CPS,            "CPS",                      "************----10-0------------")

//INST(thumb32_ENTERX,         "ENTERX",                   "************----10-0----0001----")
//INST(thumb32_LEAVEX,         "LEAVEX",                   "************----10-0----0000----")
INST(thumb32_CLREX,          "CLREX",                    "************11111000111100101111")
INST(thumb32_DSB,            "DSB",                      "************1111100011110100oooo")
INST(thumb32_DMB,            "DMB",                      "************1111100011110101oooo")
INST(thumb32_ISB,            "ISB",                      "************1111100011110110oooo")

INST(thumb32_BXJ,            "BXJ",                      "111100111100mmmm1000111100000000")
//INST(thumb32_ERET,           "ERET",                     "11110011110111101000111100000000")
//INST(thumb32_SUBS_pc_lr,     "SUBS PC, LR",              "111100111101111010001111--------")

//INST(thumb32_MRS_banked,     "MRS (banked)",             "***********-----10-0------1-----")
INST(thumb32_MRS_reg,        "MRS (reg)",                "***********R11111000dddd00000000")
//INST(thumb32_HVC,            "HVC",                      "************----1000------------")
//INST(thumb32_SMC,            "SMC",                      "************----****************")
INST(thumb32_UDF,            "UDF",                      "************----1010------------") // v6T2

// Branch instructions
INST(thumb32_BL_imm,         "BL (imm)",                 "11110Svvvvvvvvvv11j1jvvvvvvvvvvv") // v4T
INST(thumb32_BLX_imm,        "BLX (imm)",                "11110Svvvvvvvvvv11j0jvvvvvvvvvvv") // v5T
INST(thumb32_B,              "B",                        "11110Svvvvvvvvvv10j1jvvvvvvvvvvv")
INST(thumb32_UDF,            "Invalid decoding",         "11110-111-------10-0------------")
INST(thumb32_B_cond,         "B (cond)",                 "11110Sccccvvvvvv10i0ivvvvvvvvvvv")

// Store Single Data Item
INST(thumb32_STRB_imm_1,     "STRB (imm)",               "111110000000nnnntttt1PU1iiiiiiii")
INST(thumb32_STRB_imm_2,     "STRB (imm)",               "111110000000nnnntttt1100iiiiiiii")
INST(thumb32_STRB_imm_3,     "STRB (imm)",               "111110001000nnnnttttiiiiiiiiiiii")
INST(thumb32_STRBT,          "STRBT",                    "111110000000nnnntttt1110iiiiiiii")
INST(thumb32_STRB,           "STRB (reg)",               "111110000000nnnntttt000000iimmmm")
INST(thumb32_STRH_imm_1,     "STRH (imm)",               "111110000010nnnntttt1PU1iiiiiiii")
INST(thumb32_STRH_imm_2,     "STRH (imm)",               "111110000010nnnntttt1100iiiiiiii")
INST(thumb32_STRH_imm_3,     "STRH (imm)",               "111110001010nnnnttttiiiiiiiiiiii")
INST(thumb32_STRHT,          "STRHT",                    "111110000010nnnntttt1110iiiiiiii")
INST(thumb32_STRH,           "STRH (reg)",               "111110000010nnnntttt000000iimmmm")
INST(thumb32_STR_imm_1,      "STR (imm)",                "111110000100nnnntttt1PU1iiiiiiii")
INST(thumb32_STR_imm_2,      "STR (imm)",                "111110000100nnnntttt1100iiiiiiii")
INST(thumb32_STR_imm_3,      "STR (imm)",                "111110001100nnnnttttiiiiiiiiiiii")
INST(thumb32_STRT,           "STRT",                     "111110000100nnnntttt1110iiiiiiii")
INST(thumb32_STR_reg,        "STR (reg)",                "111110000100nnnntttt000000iimmmm")

// Load Byte and Memory Hints
INST(thumb32_PLD_lit,        "PLD (lit)",                "11111000U00111111111iiiiiiiiiiii")
INST(thumb32_PLD_lit,        "PLD (lit)",                "11111000U01111111111iiiiiiiiiiii")
INST(thumb32_PLD_reg,        "PLD (reg)",                "1111100000W1nnnn1111000000iimmmm")
INST(thumb32_PLD_imm8,       "PLD (imm8)",               "1111100000W1nnnn11111100iiiiiiii")
INST(thumb32_PLD_imm12,      "PLD (imm12)",              "1111100010W1nnnn1111iiiiiiiiiiii")
INST(thumb32_PLI_lit,        "PLI (lit)",                "11111001U00111111111iiiiiiiiiiii")
INST(thumb32_PLI_reg,        "PLI (reg)",                "111110010001nnnn1111000000iimmmm")
INST(thumb32_PLI_imm8,       "PLI (imm8)",               "111110010001nnnn11111100iiiiiiii")
INST(thumb32_PLI_imm12,      "PLI (imm12)",              "111110011001nnnn1111iiiiiiiiiiii")
INST(thumb32_LDRB_lit,       "LDRB (lit)",               "11111000U0011111ttttiiiiiiiiiiii")
INST(thumb32_LDRB_reg,       "LDRB (reg)",               "111110000001nnnntttt000000iimmmm")
INST(thumb32_LDRBT,          "LDRBT",                    "111110000001nnnntttt1110iiiiiiii")
INST(thumb32_LDRB_imm8,      "LDRB (imm8)",              "111110000001nnnntttt1PUWiiiiiiii")
INST(thumb32_LDRB_imm12,     "LDRB (imm12)",             "111110001001nnnnttttiiiiiiiiiiii")
INST(thumb32_LDRSB_lit,      "LDRSB (lit)",              "11111001U0011111ttttiiiiiiiiiiii")
INST(thumb32_LDRSB_reg,      "LDRSB (reg)",              "111110010001nnnntttt000000iimmmm")
INST(thumb32_LDRSBT,         "LDRSBT",                   "111110010001nnnntttt1110iiiiiiii")
INST(thumb32_LDRSB_imm8,     "LDRSB (imm8)",             "111110010001nnnntttt1PUWiiiiiiii")
INST(thumb32_LDRSB_imm12,    "LDRSB (imm12)",            "111110011001nnnnttttiiiiiiiiiiii")

// Load Halfword and Memory Hints
INST(thumb32_LDRH_lit,       "LDRH (lit)",               "11111000U0111111ttttiiiiiiiiiiii")
INST(thumb32_LDRH_reg,       "LDRH (reg)",               "111110000011nnnntttt000000iimmmm")
INST(thumb32_LDRHT,          "LDRHT",                    "111110000011nnnntttt1110iiiiiiii")
INST(thumb32_LDRH_imm8,      "LDRH (imm8)",              "111110000011nnnntttt1PUWiiiiiiii")
INST(thumb32_LDRH_imm12,     "LDRH (imm12)",             "111110001011nnnnttttiiiiiiiiiiii")
INST(thumb32_NOP,            "NOP",                      "11111001-01111111111------------")
INST(thumb32_LDRSH_lit,      "LDRSH (lit)",              "11111001U0111111ttttiiiiiiiiiiii")
INST(thumb32_NOP,            "NOP",                      "111110010011----1111000000------")
INST(thumb32_LDRSH_reg,      "LDRSH (reg)",              "111110010011nnnntttt000000iimmmm")
INST(thumb32_LDRSHT,         "LDRSHT",                   "111110010011nnnntttt1110iiiiiiii")
INST(thumb32_NOP,            "NOP",                      "111110010011----11111100--------")
INST(thumb32_NOP,            "NOP",                      "111110011011----1111------------")
INST(thumb32_LDRSH_imm8,     "LDRSH (imm8)",             "111110010011nnnntttt1PUWiiiiiiii")
INST(thumb32_LDRSH_imm12,    "LDRSH (imm12)",            "111110011011nnnnttttiiiiiiiiiiii")

// Load Word
INST(thumb32_LDR_lit,        "LDR (lit)",                "11111000U1011111ttttiiiiiiiiiiii")
INST(thumb32_LDRT,           "LDRT",                     "111110000101nnnntttt1110iiiiiiii")
INST(thumb32_LDR_reg,        "LDR (reg)",                "111110000101nnnntttt000000iimmmm")
INST(thumb32_LDR_imm8,       "LDR (imm8)",               "111110000101nnnntttt1PUWiiiiiiii")
INST(thumb32_LDR_imm12,      "LDR (imm12)",              "111110001101nnnnttttiiiiiiiiiiii")

// Data Processing (register)
INST(thumb32_LSL_reg,        "LSL (reg)",                "11111010000Smmmm1111dddd0000ssss")
INST(thumb32_LSR_reg,        "LSR (reg)",                "11111010001Smmmm1111dddd0000ssss")
INST(thumb32_ASR_reg,        "ASR (reg)",                "11111010010Smmmm1111dddd0000ssss")
INST(thumb32_ROR_reg,        "ROR (reg)",                "11111010011Smmmm1111dddd0000ssss")
INST(thumb32_SXTH,           "SXTH",                     "11111010000011111111dddd10rrmmmm")
INST(thumb32_SXTAH,          "SXTAH",                    "111110100000nnnn1111dddd10rrmmmm")
INST(thumb32_UXTH,           "UXTH",                     "11111010000111111111dddd10rrmmmm")
INST(thumb32_UXTAH,          "UXTAH",                    "111110100001nnnn1111dddd10rrmmmm")
INST(thumb32_SXTB16,         "SXTB16",                   "11111010001011111111dddd10rrmmmm")
INST(thumb32_SXTAB16,        "SXTAB16",                  "111110100010nnnn1111dddd10rrmmmm")
INST(thumb32_UXTB16,         "UXTB16",                   "11111010001111111111dddd10rrmmmm")
INST(thumb32_UXTAB16,        "UXTAB16",                  "111110100011nnnn1111dddd10rrmmmm")
INST(thumb32_SXTB,           "SXTB",                     "11111010010011111111dddd10rrmmmm")
INST(thumb32_SXTAB,          "SXTAB",                    "111110100100nnnn1111dddd10rrmmmm")
INST(thumb32_UXTB,           "UXTB",                     "11111010010111111111dddd10rrmmmm")
INST(thumb32_UXTAB,          "UXTAB",                    "111110100101nnnn1111dddd10rrmmmm")

// Parallel Addition and Subtraction (signed)
INST(thumb32_SADD16,         "SADD16",                   "111110101001nnnn1111dddd0000mmmm")
INST(thumb32_SASX,           "SASX",                     "111110101010nnnn1111dddd0000mmmm")
INST(thumb32_SSAX,           "SSAX",                     "111110101110nnnn1111dddd0000mmmm")
INST(thumb32_SSUB16,         "SSUB16",                   "111110101101nnnn1111dddd0000mmmm")
INST(thumb32_SADD8,          "SADD8",                    "111110101000nnnn1111dddd0000mmmm")
INST(thumb32_SSUB8,          "SSUB8",                    "111110101100nnnn1111dddd0000mmmm")
INST(thumb32_QADD16,         "QADD16",                   "111110101001nnnn1111dddd0001mmmm")
INST(thumb32_QASX,           "QASX",                     "111110101010nnnn1111dddd0001mmmm")
INST(thumb32_QSAX,           "QSAX",                     "111110101110nnnn1111dddd0001mmmm")
INST(thumb32_QSUB16,         "QSUB16",                   "111110101101nnnn1111dddd0001mmmm")
INST(thumb32_QADD8,          "QADD8",                    "111110101000nnnn1111dddd0001mmmm")
INST(thumb32_QSUB8,          "QSUB8",                    "111110101100nnnn1111dddd0001mmmm")
INST(thumb32_SHADD16,        "SHADD16",                  "111110101001nnnn1111dddd0010mmmm")
INST(thumb32_SHASX,          "SHASX",                    "111110101010nnnn1111dddd0010mmmm")
INST(thumb32_SHSAX,          "SHSAX",                    "111110101110nnnn1111dddd0010mmmm")
INST(thumb32_SHSUB16,        "SHSUB16",                  "111110101101nnnn1111dddd0010mmmm")
INST(thumb32_SHADD8,         "SHADD8",                   "111110101000nnnn1111dddd0010mmmm")
INST(thumb32_SHSUB8,         "SHSUB8",                   "111110101100nnnn1111dddd0010mmmm")

// Parallel Addition and Subtraction (unsigned)
INST(thumb32_UADD16,         "UADD16",                   "111110101001nnnn1111dddd0100mmmm")
INST(thumb32_UASX,           "UASX",                     "111110101010nnnn1111dddd0100mmmm")
INST(thumb32_USAX,           "USAX",                     "111110101110nnnn1111dddd0100mmmm")
INST(thumb32_USUB16,         "USUB16",                   "111110101101nnnn1111dddd0100mmmm")
INST(thumb32_UADD8,          "UADD8",                    "111110101000nnnn1111dddd0100mmmm")
INST(thumb32_USUB8,          "USUB8",                    "111110101100nnnn1111dddd0100mmmm")
INST(thumb32_UQADD16,        "UQADD16",                  "111110101001nnnn1111dddd0101mmmm")
INST(thumb32_UQASX,          "UQASX",                    "111110101010nnnn1111dddd0101mmmm")
INST(thumb32_UQSAX,          "UQSAX",                    "111110101110nnnn1111dddd0101mmmm")
INST(thumb32_UQSUB16,        "UQSUB16",                  "111110101101nnnn1111dddd0101mmmm")
INST(thumb32_UQADD8,         "UQADD8",                   "111110101000nnnn1111dddd0101mmmm")
INST(thumb32_UQSUB8,         "UQSUB8",                   "111110101100nnnn1111dddd0101mmmm")
INST(thumb32_UHADD16,        "UHADD16",                  "111110101001nnnn1111dddd0110mmmm")
INST(thumb32_UHASX,          "UHASX",                    "111110101010nnnn1111dddd0110mmmm")
INST(thumb32_UHSAX,          "UHSAX",                    "111110101110nnnn1111dddd0110mmmm")
INST(thumb32_UHSUB16,        "UHSUB16",                  "111110101101nnnn1111dddd0110mmmm")
INST(thumb32_UHADD8,         "UHADD8",                   "111110101000nnnn1111dddd0110mmmm")
INST(thumb32_UHSUB8,         "UHSUB8",                   "111110101100nnnn1111dddd0110mmmm")

// Miscellaneous Operations
INST(thumb32_QADD,           "QADD",                     "111110101000nnnn1111dddd1000mmmm")
INST(thumb32_QDADD,          "QDADD",                    "111110101000nnnn1111dddd1001mmmm")
INST(thumb32_QSUB,           "QSUB",                     "111110101000nnnn1111dddd1010mmmm")
INST(thumb32_QDSUB,          "QDSUB",                    "111110101000nnnn1111dddd1011mmmm")
INST(thumb32_REV,            "REV",                      "111110101001nnnn1111dddd1000mmmm")
INST(thumb32_REV16,          "REV16",                    "111110101001nnnn1111dddd1001mmmm")
INST(thumb32_RBIT,           "RBIT",                     "111110101001nnnn1111dddd1010mmmm")
INST(thumb32_REVSH,          "REVSH",                    "111110101001nnnn1111dddd1011mmmm")
INST(thumb32_SEL,            "SEL",                      "111110101010nnnn1111dddd1000mmmm")
INST(thumb32_CLZ,            "CLZ",                      "111110101011nnnn1111dddd1000mmmm")

// Multiply, Multiply Accumulate, and Absolute Difference
INST(thumb32_MUL,            "MUL",                      "111110110000nnnn1111dddd0000mmmm")
INST(thumb32_MLA,            "MLA",                      "111110110000nnnnaaaadddd0000mmmm")
INST(thumb32_MLS,            "MLS",                      "111110110000nnnnaaaadddd0001mmmm")
INST(thumb32_SMULXY,         "SMULXY",                   "111110110001nnnn1111dddd00NMmmmm")
INST(thumb32_SMLAXY,         "SMLAXY",                   "111110110001nnnnaaaadddd00NMmmmm")
INST(thumb32_SMUAD,          "SMUAD",                    "111110110010nnnn1111dddd000Mmmmm")
INST(thumb32_SMLAD,          "SMLAD",                    "111110110010nnnnaaaadddd000Xmmmm")
INST(thumb32_SMULWY,         "SMULWY",                   "111110110011nnnn1111dddd000Mmmmm")
INST(thumb32_SMLAWY,         "SMLAWY",                   "111110110011nnnnaaaadddd000Mmmmm")
INST(thumb32_SMUSD,          "SMUSD",                    "111110110100nnnn1111dddd000Mmmmm")
INST(thumb32_SMLSD,          "SMLSD",                    "111110110100nnnnaaaadddd000Xmmmm")
INST(thumb32_SMMUL,          "SMMUL",                    "111110110101nnnn1111dddd000Rmmmm")
INST(thumb32_SMMLA,          "SMMLA",                    "111110110101nnnnaaaadddd000Rmmmm")
INST(thumb32_SMMLS,          "SMMLS",                    "111110110110nnnnaaaadddd000Rmmmm")
INST(thumb32_USAD8,          "USAD8",                    "111110110111nnnn1111dddd0000mmmm")
INST(thumb32_USADA8,         "USADA8",                   "111110110111nnnnaaaadddd0000mmmm")

// Long Multiply, Long Multiply Accumulate, and Divide
INST(thumb32_SMULL,          "SMULL",                    "111110111000nnnnllllhhhh0000mmmm")
INST(thumb32_SDIV,           "SDIV",                     "111110111001nnnn1111dddd1111mmmm")
INST(thumb32_UMULL,          "UMULL",                    "111110111010nnnnllllhhhh0000mmmm")
INST(thumb32_UDIV,           "UDIV",                     "111110111011nnnn1111dddd1111mmmm")
INST(thumb32_SMLAL,          "SMLAL",                    "111110111100nnnnllllhhhh0000mmmm")
INST(thumb32_SMLALXY,        "SMLALXY",                  "111110111100nnnnllllhhhh10NMmmmm")
INST(thumb32_SMLALD,         "SMLALD",                   "111110111100nnnnllllhhhh110Mmmmm")
INST(thumb32_SMLSLD,         "SMLSLD",                   "111110111101nnnnllllhhhh110Mmmmm")
INST(thumb32_UMLAL,          "UMLAL",                    "111110111110nnnnllllhhhh0000mmmm")
INST(thumb32_UMAAL,          "UMAAL",                    "111110111110nnnnllllhhhh0110mmmm")

// Coprocessor
INST(thumb32_MCRR,           "MCRR",                     "111o11000100uuuuttttppppooooMMMM")
INST(thumb32_MRRC,           "MRRC",                     "111o11000101uuuuttttppppooooMMMM")
INST(thumb32_STC,            "STC",                      "111o110pudw0nnnnDDDDppppvvvvvvvv")
INST(thumb32_LDC,            "LDC",                      "111o110pudw1nnnnDDDDppppvvvvvvvv")
INST(thumb32_CDP,            "CDP",                      "111o1110ooooNNNNDDDDppppooo0MMMM")
INST(thumb32_MCR,            "MCR",                      "111o1110ooo0NNNNttttppppooo1MMMM")
INST(thumb32_MRC,            "MRC",                      "111o1110ooo1NNNNttttppppooo1MMMM")
