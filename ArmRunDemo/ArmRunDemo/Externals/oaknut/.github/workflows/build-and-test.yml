on: [push, pull_request]

jobs:
  test_on_ubuntu:
    runs-on: ubuntu-latest
    name: g++-10 (aarch64)

    steps:
      - name: Checkout oaknut repo
        uses: actions/checkout@v3

      - name: Update package repositories
        run: sudo apt-get update

      - name: Install dependencies
        run: >
          sudo apt-get install -q -y
          gcc-10-aarch64-linux-gnu
          g++-10-aarch64-linux-gnu
          ninja-build
          qemu-user

      - name: Checkout qemu
        uses: actions/checkout@v3
        with:
          repository: qemu/qemu
          ref: v8.1.2
          path: externals/qemu

      - name: Build qemu
        working-directory: externals/qemu
        run: |
          sudo apt-get install git libglib2.0-dev libfdt-dev libpixman-1-dev zlib1g-dev ninja-build
          mkdir build
          cd build
          ../configure --target-list=aarch64-linux-user
          make -j4 qemu-aarch64

      - name: Checkout Catch2 v3 repo
        uses: actions/checkout@v3
        with:
          repository: catchorg/Catch2
          ref: v3.2.0
          path: externals/catch

      - name: Configure CMake
        env:
          CC: aarch64-linux-gnu-gcc-10
          CXX: aarch64-linux-gnu-g++-10
        run: >
          cmake
          -B ${{github.workspace}}/build
          -H.
          -GNinja
          -DOAKNUT_USE_BUNDLED_CATCH=ON

      - name: Build
        working-directory: ${{github.workspace}}/build
        run: ninja

      - name: Test
        working-directory: ${{github.workspace}}/build
        run: ../externals/qemu/build/qemu-aarch64 -L /usr/aarch64-linux-gnu ./oaknut-tests -d yes

  test_on_windows:
    runs-on: windows-latest
    name: msvc-arm64

    steps:
      - name: Checkout oaknut repo
        uses: actions/checkout@v3

      - name: Checkout Catch2 v3 repo
        uses: actions/checkout@v3
        with:
          repository: catchorg/Catch2
          ref: v3.2.0
          path: externals/catch

      - name: Setup msvc-arm64 environment
        uses: ilammy/msvc-dev-cmd@v1
        with:
          arch: amd64_arm64

      - name: Configure CMake
        run: >
          cmake
          -B ${{github.workspace}}/build
          -GNinja
          -DOAKNUT_USE_BUNDLED_CATCH=ON

      - name: Build
        working-directory: ${{github.workspace}}/build
        run: cmake --build . --config Release

  test_on_macos:
    runs-on: macos-latest
    name: macos-arm64

    steps:
      - name: Checkout oaknut repo
        uses: actions/checkout@v3

      - name: Checkout Catch2 v3 repo
        uses: actions/checkout@v3
        with:
          repository: catchorg/Catch2
          ref: v3.2.0
          path: externals/catch

      - name: Install dependencies
        run: |
          brew install ninja

      - name: Configure CMake
        run: >
          cmake
          -B ${{github.workspace}}/build
          -GNinja
          -DCMAKE_OSX_ARCHITECTURES=arm64
          -DOAKNUT_USE_BUNDLED_CATCH=ON

      - name: Build
        working-directory: ${{github.workspace}}/build
        run: cmake --build . --config Release

  test_on_freebsd:
    runs-on: ubuntu-latest
    name: freebsd-arm64

    steps:
      - name: Checkout oaknut repo
        uses: actions/checkout@v3

      - name: Build and Test
        uses: cross-platform-actions/action@v0.19.1
        with:
          operating_system: freebsd
          architecture: arm64
          version: '13.2'
          shell: bash
          run: |
            pwd
            sudo pkg update
            sudo pkg install -y catch2 cmake ninja
            cmake -B ${{github.workspace}}/build -GNinja
            cd build
            cmake --build . --config Release
            ./oaknut-tests -d yes

  test_on_openbsd:
    runs-on: ubuntu-latest
    name: openbsd-arm64

    steps:
      - name: Checkout oaknut repo
        uses: actions/checkout@v3

      - name: Build and Test
        uses: cross-platform-actions/action@v0.19.1
        with:
          operating_system: openbsd
          architecture: arm64
          version: '7.3'
          shell: bash
          run: |
            pwd
            sudo pkg_add catch2 cmake ninja
            cmake -B ${{github.workspace}}/build -GNinja
            cd build
            cmake --build . --config Release
            ./oaknut-tests -d yes "~[slow]"

  test_on_android:
    runs-on: ubuntu-latest
    name: android

    steps:
      - name: Checkout oaknut repo
        uses: actions/checkout@v3

      - name: Update package repositories
        run: sudo apt-get update

      - name: Install dependencies
        run: sudo apt-get install -q -y ninja-build

      - name: Checkout Catch2 v3 repo
        uses: actions/checkout@v3
        with:
          repository: catchorg/Catch2
          ref: v3.2.0
          path: externals/catch

      - name: Configure CMake
        run: >
          cmake
          -B ${{github.workspace}}/build
          -H.
          -GNinja
          -DANDROID_ABI=arm64-v8a
          -DANDROID_PLATFORM=30
          -DCMAKE_BUILD_TYPE=Release
          -DCMAKE_TOOLCHAIN_FILE=$ANDROID_NDK/build/cmake/android.toolchain.cmake
          -DOAKNUT_USE_BUNDLED_CATCH=ON

      - name: Build
        working-directory: ${{github.workspace}}/build
        run: cmake --build . --config Release

  x86_64:
    runs-on: ubuntu-latest
    name: x86_64

    steps:
      - name: Checkout oaknut repo
        uses: actions/checkout@v3

      - name: Update package repositories
        run: sudo apt-get update

      - name: Install dependencies
        run: sudo apt-get install -q -y ninja-build

      - name: Checkout Catch2 v3 repo
        uses: actions/checkout@v3
        with:
          repository: catchorg/Catch2
          ref: v3.2.0
          path: externals/catch

      - name: Configure CMake
        run: >
          cmake
          -B ${{github.workspace}}/build
          -H.
          -GNinja
          -DOAKNUT_USE_BUNDLED_CATCH=ON

      - name: Build
        working-directory: ${{github.workspace}}/build
        run: ninja

      - name: Test
        working-directory: ${{github.workspace}}/build
        run: ./oaknut-tests -d yes
