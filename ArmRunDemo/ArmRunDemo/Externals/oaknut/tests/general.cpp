// SPDX-FileCopyrightText: Copyright (c) 2022 merryhime <https://mary.rs>
// SPDX-License-Identifier: MIT

#include <array>
#include <cstdint>

#include <catch2/catch_test_macros.hpp>

#include "oaknut/oaknut.hpp"

#define T(HEX, CMD)                   \
    TEST_CASE(#CMD)                   \
    {                                 \
        using namespace oaknut;       \
        using namespace oaknut::util; \
                                      \
        std::uint32_t result;         \
        CodeGenerator code{&result};  \
                                      \
        code.CMD;                     \
                                      \
        REQUIRE(result == HEX);       \
    }

T(0x1a0f01c3, ADC(W3, W14, W15))
T(0x1a1803a5, ADC(W5, W29, W24))
T(0x1a0f02b3, ADC(W19, W21, W15))
T(0x9a0101a0, ADC(X0, X13, X1))
T(0x9a0e01e7, ADC(X7, X15, X14))
T(0x9a18031e, ADC(X30, X24, X24))
T(0x3a04030d, ADCS(W13, W24, W4))
T(0x3a0f0159, ADCS(W25, W10, W15))
T(0x3a070042, ADCS(W2, W2, W7))
T(0xba1003ce, ADCS(X14, X30, X16))
T(0xba190368, ADCS(X8, X27, X25))
T(0xba0d01e1, ADCS(X1, X15, X13))
T(0x0b3c11db, ADD(W27, W14, W28, UXTB, 4))
T(0x0b27e567, ADD(W7, W11, W7, SXTX, 1))
T(0x0b232890, ADD(W16, W4, W3, UXTH, 2))
T(0x8b218f83, ADD(X3, X28, W1, SXTB, 3))
T(0x8b21d262, ADD(X2, X19, W1, SXTW, 4))
T(0x11549545, ADD(W5, W10, 1317, LSL, 12))
T(0x1170da5c, ADD(W28, W18, 3126, LSL, 12))
T(0x113d5da2, ADD(W2, W13, 3927))
T(0x91204aa8, ADD(X8, X21, 2066))
T(0x9123368b, ADD(X11, X20, 2253))
T(0x917f9912, ADD(X18, X8, 4070, LSL, 12))
T(0x0b874fb9, ADD(W25, W29, W7, ASR, 19))
T(0x0b446a2c, ADD(W12, W17, W4, LSR, 26))
T(0x8b494f4d, ADD(X13, X26, X9, LSR, 19))
T(0x8b5ff514, ADD(X20, X8, XZR, LSR, 61))
T(0x2b3e2aa2, ADDS(W2, W21, W30, UXTH, 2))
T(0x2b34e14a, ADDS(W10, W10, W20, SXTX))
T(0x2b2a6565, ADDS(W5, W11, W10, UXTX, 1))
T(0xab226edb, ADDS(X27, X22, X2, UXTX, 3))
T(0xab29b19a, ADDS(X26, X12, W9, SXTH, 4))
T(0xab3583f0, ADDS(X16, SP, W21, SXTB))
T(0x31592de5, ADDS(W5, W15, 1611, LSL, 12))
T(0x3166dcdd, ADDS(W29, W6, 2487, LSL, 12))
T(0x312f1c18, ADDS(W24, W0, 3015))
T(0xb158ff7c, ADDS(X28, X27, 1599, LSL, 12))
T(0xb108b6af, ADDS(X15, X21, 557))
T(0xb106c869, ADDS(X9, X3, 434))
T(0x2b5b05a8, ADDS(W8, W13, W27, LSR, 1))
T(0xab52ca62, ADDS(X2, X19, X18, LSR, 50))
// T(0x30075141, ADR(X1, 59945))
// T(0x100fd28d, ADR(X13, 129616))
// T(0x7075db19, ADR(X25, 965475))
// T(0xb0e688f1, ADRP(X17, -854470656))
// T(0xf03be0fc, ADRP(X28, 2009198592))
// T(0x90cbd7d4, ADRP(X20, -1750106112))
T(0x12071254, AND(W20, W18, 0x3e000000))
T(0x1206a3a4, AND(W4, W29, 0xfc07fc07))
T(0x12185f4b, AND(W11, W26, 0xffffff00))
T(0x924f7a4f, AND(X15, X18, 0xfffe00000000ffff))
T(0x926122c3, AND(X3, X22, 0xff80000000))
T(0x9265fbce, AND(X14, X30, 0xfffffffffbffffff))
T(0x0a071d7d, AND(W29, W11, W7, LSL, 7))
T(0x0a5376ff, AND(WZR, W23, W19, LSR, 29))
T(0x0a5900b8, AND(W24, W5, W25, LSR, 0))
T(0x8ad9dc44, AND(X4, X2, X25, ROR, 55))
T(0x8a872dcc, AND(X12, X14, X7, ASR, 11))
T(0x8a10d18f, AND(X15, X12, X16, LSL, 52))
T(0x72086584, ANDS(W4, W12, 0xff03ffff))
T(0x720e3614, ANDS(W20, W16, 0xfffc0000))
T(0x720a7a7e, ANDS(W30, W19, 0xffdfffff))
T(0xf20e4d1b, ANDS(X27, X8, 0xfffc003ffffc003f))
T(0xf21d6250, ANDS(X16, X18, 0xffffff80ffffff8))
T(0xf251019a, ANDS(X26, X12, 0x800000000000))
T(0x6a5875b7, ANDS(W23, W13, W24, LSR, 29))
T(0x6a5937b7, ANDS(W23, W29, W25, LSR, 13))
T(0xead7637b, ANDS(X27, X27, X23, ROR, 24))
T(0xea11f241, ANDS(X1, X18, X17, LSL, 60))
T(0xeacfca26, ANDS(X6, X17, X15, ROR, 50))
T(0x13167d11, ASR(W17, W8, 22))
T(0x9347ff0d, ASR(X13, X24, 7))
T(0x9361feb4, ASR(X20, X21, 33))
T(0x9356fda2, ASR(X2, X13, 22))
T(0x1ac4280a, ASR(W10, W0, W4))
T(0x1ada2913, ASR(W19, W8, W26))
T(0x1ac92800, ASR(W0, W0, W9))
T(0x9ad228b9, ASR(X25, X5, X18))
T(0x9acf29f8, ASR(X24, X15, X15))
T(0x9ac82afa, ASR(X26, X23, X8))
T(0x1ac7293c, ASRV(W28, W9, W7))
T(0x1ace2a6d, ASRV(W13, W19, W14))
T(0x1ac929e6, ASRV(W6, W15, W9))
T(0x9acc2a99, ASRV(X25, X20, X12))
T(0x9ac129ba, ASRV(X26, X13, X1))
T(0x9ac22a3f, ASRV(XZR, X17, X2))
// T(0xd50f78af, AT(SYS 7, C7, C8, 5, X15))
// T(0xd50c79a4, AT(SYS 4, C7, C9, 5, X4))
// T(0xd5087926, AT(SYS 0, C7, C9, 1, X6))
T(0x161e54bf, B(-126266628))
T(0x17a62057, B(-23559844))
T(0x16345f90, B(-120488384))
T(0x5460c564, B(MI, 792748))
T(0x5436d620, B(EQ, 449220))
T(0x54036427, B(VC, 27780))
T(0xb3641538, BFI(X24, X9, 28, 6))
T(0xb34a08a7, BFI(X7, X5, 54, 3))
T(0xb34c03e9, BFI(X9, XZR, 52, 1))
T(0xb36e6c95, BFI(X21, X4, 18, 28))
T(0xb3621d4f, BFI(X15, X10, 30, 8))
T(0x330313fb, BFXIL(W27, WZR, 3, 2))
T(0xb35fc877, BFXIL(X23, X3, 31, 20))
T(0xb357d093, BFXIL(X19, X4, 23, 30))
T(0xb377f01b, BFXIL(X27, X0, 55, 6))
T(0xb377e391, BFXIL(X17, X28, 55, 2))
T(0xa3920b0, BIC(W16, W5, W25, LSL, 8))
T(0x8a29fb68, BIC(X8, X27, X9, LSL, 62))
T(0x8aa8e15d, BIC(X29, X10, X8, ASR, 56))
T(0x8ab733b6, BIC(X22, X29, X23, ASR, 12))
T(0x6a7b1ff9, BICS(W25, WZR, W27, LSR, 7))
T(0xea3d86fd, BICS(X29, X23, X29, LSL, 33))
T(0xeaf4af4c, BICS(X12, X26, X20, ROR, 43))
T(0xeaf46b96, BICS(X22, X28, X20, ROR, 26))
T(0x9708781e, BL(-64888712))
T(0x95b27a60, BL(113895808))
T(0x956ab105, BL(95077396))
T(0xd63f0160, BLR(X11))
T(0xd63f0220, BLR(X17))
T(0xd61f0280, BR(X20))
T(0xd61f0260, BR(X19))
T(0xd61f0320, BR(X25))
T(0xd433f1c0, BRK(0x9f8e))
T(0xd43f7820, BRK(0xfbc1))
T(0xd43127c0, BRK(0x893e))
T(0x35a95593, CBNZ(W19, -709968))
T(0x358d6ef5, CBNZ(W21, -938532))
T(0x358d05f9, CBNZ(W25, -941892))
T(0xb5bfb71c, CBNZ(X28, -526624))
T(0xb5e62555, CBNZ(X21, -211800))
T(0xb5b24165, CBNZ(X5, -636884))
T(0x34d12762, CBZ(W2, -383764))
T(0x34e9aa59, CBZ(W25, -182968))
T(0x3495c7a7, CBZ(W7, -870156))
T(0xb429394d, CBZ(X13, 337704))
T(0xb4acb60f, CBZ(X15, -682304))
T(0xb445904f, CBZ(X15, 569864))
T(0x3a406be9, CCMN(WZR, 0, 9, VS))
T(0x3a4cb9ef, CCMN(W15, 12, 15, LT))
T(0x3a483a2f, CCMN(W17, 8, 15, LO))
T(0xba55e821, CCMN(X1, 21, 1, AL))
T(0xba5fba49, CCMN(X18, 31, 9, LT))
T(0xba4c4947, CCMN(X10, 12, 7, MI))
T(0x3a44336e, CCMN(W27, W4, 14, LO))
T(0x3a4a9046, CCMN(W2, W10, 6, LS))
T(0x3a4390ce, CCMN(W6, W3, 14, LS))
T(0xba55e225, CCMN(X17, X21, 5, AL))
T(0xba52438d, CCMN(X28, X18, 13, MI))
T(0xba591164, CCMN(X11, X25, 4, NE))
T(0x7a43ea0e, CCMP(W16, 3, 14, AL))
T(0x7a57fb0f, CCMP(W24, 23, 15, NV))
T(0x7a53e9ce, CCMP(W14, 19, 14, AL))
T(0xfa4a0ba7, CCMP(X29, 10, 7, EQ))
T(0xfa49daae, CCMP(X21, 9, 14, LE))
T(0xfa438b89, CCMP(X28, 3, 9, HI))
T(0x7a4253cc, CCMP(W30, W2, 12, PL))
T(0x7a48610b, CCMP(W8, W8, 11, VS))
T(0x7a5400c3, CCMP(W6, W20, 3, EQ))
T(0xfa44936d, CCMP(X27, X4, 13, LS))
T(0xfa46220a, CCMP(X16, X6, 10, HS))
T(0xfa46404c, CCMP(X2, X6, 12, MI))
T(0x1a92b680, CSINC(W0, W20, W18, LT))
T(0x1a87651d, CSINC(W29, W8, W7, VS))
T(0x1a8bb43a, CSINC(W26, W1, W11, LT))
T(0x9a92e6c4, CSINC(X4, X22, X18, AL))
T(0x9a99a75c, CSINC(X28, X26, X25, GE))
T(0x9a993524, CSINC(X4, X9, X25, LO))
T(0x5a92532b, CSINV(W11, W25, W18, PL))
T(0x5a987219, CSINV(W25, W16, W24, VC))
T(0x5a805309, CSINV(W9, W24, W0, PL))
T(0xda8bb1b1, CSINV(X17, X13, X11, LT))
T(0xda971187, CSINV(X7, X12, X23, NE))
T(0xda944246, CSINV(X6, X18, X20, MI))
T(0xd503315f, CLREX(1))
T(0xd5033a5f, CLREX(10))
T(0x5ac0145b, CLS(W27, W2))
T(0x5ac0152e, CLS(W14, W9))
T(0x5ac01530, CLS(W16, W9))
T(0xdac0145b, CLS(X27, X2))
T(0xdac014b4, CLS(X20, X5))
T(0xdac0150d, CLS(X13, X8))
T(0x5ac01274, CLZ(W20, W19))
T(0x5ac010fc, CLZ(W28, W7))
T(0x5ac01106, CLZ(W6, W8))
T(0xdac0105e, CLZ(X30, X2))
T(0xdac013c3, CLZ(X3, X30))
T(0xdac010f9, CLZ(X25, X7))
T(0xab9b277f, CMN(X27, X27, ASR, 9))
T(0xab25653f, CMN(X9, X5, UXTX, 1))
T(0xab2cd13f, CMN(X9, W12, SXTW, 4))
T(0x313834df, CMN(W6, 3597))
T(0x317958bf, CMN(W5, 3670, LSL, 12))
T(0x3133573f, CMN(W25, 3285))
T(0xb15ccd7f, CMN(X11, 1843, LSL, 12))
T(0xb153569f, CMN(X20, 1237, LSL, 12))
T(0xb17d193f, CMN(X9, 3910, LSL, 12))
T(0x2b9b225f, CMN(W18, W27, ASR, 8))
T(0xab4cfd9f, CMN(X12, X12, LSR, 63))
T(0xab56fcbf, CMN(X5, X22, LSR, 63))
T(0x6b35c6df, CMP(W22, W21, SXTW, 1))
T(0xeb24a7ff, CMP(SP, W4, SXTH, 1))
T(0xeb32d15f, CMP(X10, W18, SXTW, 4))
T(0x717d7a5f, CMP(W18, 3934, LSL, 12))
T(0x7153bcdf, CMP(W6, 1263, LSL, 12))
T(0x716584ff, CMP(W7, 2401, LSL, 12))
T(0xf15a64bf, CMP(X5, 1689, LSL, 12))
T(0xf179805f, CMP(X2, 3680, LSL, 12))
T(0xf12c6a7f, CMP(X19, 2842))
T(0xeb4b65bf, CMP(X13, X11, LSR, 25))
T(0xeb06373f, CMP(X25, X6, LSL, 13))
T(0x5a89558e, CSNEG(W14, W12, W9, PL))
T(0x5a8b16d7, CSNEG(W23, W22, W11, NE))
T(0x5a92345a, CSNEG(W26, W2, W18, LO))
T(0xda93d75c, CSNEG(X28, X26, X19, LE))
T(0xda9234c2, CSNEG(X2, X6, X18, LO))
T(0xda9465af, CSNEG(X15, X13, X20, VS))
T(0xd503229f, CSDB())
T(0x1a9f4099, CSEL(W25, W4, WZR, MI))
T(0x1a8b3138, CSEL(W24, W9, W11, LO))
T(0x1a8c3041, CSEL(W1, W2, W12, LO))
T(0x9a9d60d8, CSEL(X24, X6, X29, VS))
T(0x9a92e2c8, CSEL(X8, X22, X18, AL))
T(0x9a92a195, CSEL(X21, X12, X18, GE))
T(0x1a9fd7fa, CSET(W26, GT))
T(0x1a9f47ec, CSET(W12, PL))
T(0x1a9f87f0, CSET(W16, LS))
T(0x9a9ff7ff, CSINC(XZR, XZR, XZR, NV))
T(0x9a9f17f1, CSET(X17, EQ))
T(0x9a9fb7ed, CSET(X13, GE))
T(0x5a9f93e3, CSETM(W3, HI))
T(0x5a9ff3f3, CSINV(W19, WZR, WZR, NV))
T(0x5a9fa3f4, CSETM(W20, LT))
T(0xda9f63e1, CSETM(X1, VC))
T(0xda9f23ea, CSETM(X10, LO))
T(0xda9f83ef, CSETM(X15, LS))
T(0x1a9f14f1, CSINC(W17, W7, WZR, NE))
T(0x1a93d72a, CSINC(W10, W25, W19, LE))
T(0x1a87d57f, CSINC(WZR, W11, W7, LE))
T(0x9a89d45e, CSINC(X30, X2, X9, LE))
T(0x9a9ed5dc, CSINC(X28, X14, X30, LE))
T(0x9a9746e8, CINC(X8, X23, PL))
T(0x5a89f1de, CSINV(W30, W14, W9, NV))
T(0x5a9a12ae, CSINV(W14, W21, W26, NE))
T(0x5a9d728b, CSINV(W11, W20, W29, VC))
T(0xda80e3b0, CSINV(X16, X29, X0, AL))
T(0xda8102e3, CSINV(X3, X23, X1, EQ))
T(0xda9692d8, CINV(X24, X22, HI))
T(0x5a8417bc, CSNEG(W28, W29, W4, NE))
T(0x5a86d5fb, CSNEG(W27, W15, W6, LE))
T(0x5a8b0768, CSNEG(W8, W27, W11, EQ))
T(0xda9145c6, CSNEG(X6, X14, X17, MI))
T(0xda8da6b7, CSNEG(X23, X21, X13, GE))
T(0xda9f65c2, CSNEG(X2, X14, XZR, VS))
// T(0xd50875bc, DC(SYS 0, C7, C5, 5, X28))
// T(0xd508722b, DC(SYS 0, C7, C2, 1, X11))
// T(0xd5087c11, DC(SYS 0, C7, C12, 0, X17))
T(0xd4a82a21, DCPS1(0x4151))
T(0xd4ba5161, DCPS1(0xd28b))
T(0xd4b8bae1, DCPS1(0xc5d7))
T(0xd4b40a62, DCPS2(0xa053))
T(0xd4b46722, DCPS2(0xa339))
T(0xd4af6fa2, DCPS2(0x7b7d))
T(0xd50333bf, DMB(BarrierOp::OSH))
T(0xd5033dbf, DMB(BarrierOp::LD))
T(0xd6bf03e0, DRPS())
T(0xd503369f, DSB(BarrierOp::NSHST))
T(0x4aa16b70, EON(W16, W27, W1, ASR, 26))
T(0xcabea85b, EON(X27, X2, X30, ASR, 42))
T(0xcaf8f8b5, EON(X21, X5, X24, ROR, 62))
T(0xca6b0eec, EON(X12, X23, X11, LSR, 3))
T(0x52127454, EOR(W20, W2, 0xffffcfff))
T(0x52071ca2, EOR(W2, W5, 0xfe000001))
T(0x52065dfd, EOR(W29, W15, 0xfc03ffff))
T(0xd27443ce, EOR(X14, X30, 0x1ffff000))
T(0xd2594c38, EOR(X24, X1, 0x7ffff8000000000))
T(0x4aca4192, EOR(W18, W12, W10, ROR, 16))
T(0x4a16409b, EOR(W27, W4, W22, LSL, 16))
T(0x4a1b2c0e, EOR(W14, W0, W27, LSL, 11))
T(0xca1512ba, EOR(X26, X21, X21, LSL, 4))
T(0xcadca40f, EOR(X15, X0, X28, ROR, 41))
T(0xca450f15, EOR(X21, X24, X5, LSR, 3))
T(0xd69f03e0, ERET())
T(0x138122a5, EXTR(W5, W21, W1, 8))
T(0x13815c7e, EXTR(W30, W3, W1, 23))
T(0x138b0722, EXTR(W2, W25, W11, 1))
T(0x93dc0da7, EXTR(X7, X13, X28, 3))
T(0x93c7929c, EXTR(X28, X20, X7, 36))
T(0x93dd555c, EXTR(X28, X10, X29, 21))
T(0xd50323df, HINT(30))
T(0xd503265f, HINT(50))
T(0xd50325bf, HINT(45))
T(0xd456b700, HLT(0xb5b8))
T(0xd45f5860, HLT(0xfac3))
T(0xd44658a0, HLT(0x32c5))
T(0xd4060fa2, HVC(0x307d))
T(0xd41b5ae2, HVC(0xdad7))
T(0xd40cec82, HVC(0x6764))
// T(0xd50f74db, IC(SYS 7, C7, C4, 6, X27))
// T(0xd50d76eb, IC(SYS 5, C7, C6, 7, X11))
// T(0xd5087a7d, IC(SYS 0, C7, C10, 3, X29))
T(0xd5033cdf, ISB((BarrierOp)12))
T(0xd50339df, ISB((BarrierOp)9))
T(0xd5033fdf, ISB())
T(0x88dffded, LDAR(W13, X15))
T(0x88dffe4a, LDAR(W10, X18))
T(0x88dffcfe, LDAR(W30, X7))
T(0xc8dffd7e, LDAR(X30, X11))
T(0xc8dffdad, LDAR(X13, X13))
T(0xc8dfff64, LDAR(X4, X27))
T(0x08dffd0e, LDARB(W14, X8))
T(0x08dffe0b, LDARB(W11, X16))
T(0x08dffea3, LDARB(W3, X21))
T(0x48dffe3d, LDARH(W29, X17))
T(0x48dffc6f, LDARH(W15, X3))
T(0x48dffd77, LDARH(W23, X11))
T(0x887f85e3, LDAXP(W3, W1, X15))
T(0x887ffbb0, LDAXP(W16, W30, X29))
T(0x887fa8bb, LDAXP(W27, W10, X5))
T(0xc87fd995, LDAXP(X21, X22, X12))
T(0xc87fa4cd, LDAXP(X13, X9, X6))
T(0xc87fab62, LDAXP(X2, X10, X27))
T(0x885ffcad, LDAXR(W13, X5))
T(0x885ffd32, LDAXR(W18, X9))
T(0x885fffb8, LDAXR(W24, X29))
T(0xc85fff47, LDAXR(X7, X26))
T(0xc85ffeba, LDAXR(X26, X21))
T(0xc85ffdd3, LDAXR(X19, X14))
T(0x085ffe8a, LDAXRB(W10, X20))
T(0x085ffcab, LDAXRB(W11, X5))
T(0x085fffc4, LDAXRB(W4, X30))
T(0x485ffe71, LDAXRH(W17, X19))
T(0x485ffc80, LDAXRH(W0, X4))
T(0x485fffe2, LDAXRH(W2, SP))
T(0x287aba7d, LDNP(W29, W14, X19, -44))
T(0x28501c01, LDNP(W1, W7, X0, 128))
T(0x2844abfd, LDNP(W29, W10, SP, 36))
T(0xa86e004b, LDNP(X11, X0, X2, -288))
T(0xa8674a62, LDNP(X2, X18, X19, -400))
T(0xa8725931, LDNP(X17, X22, X9, -224))
T(0x28de404b, LDP(W11, W16, X2, POST_INDEXED, 240))
T(0x28f72aed, LDP(W13, W10, X23, POST_INDEXED, -72))
T(0x28d4aff5, LDP(W21, W11, SP, POST_INDEXED, 164))
T(0xa8e14240, LDP(X0, X16, X18, POST_INDEXED, -496))
T(0xa8ee6fd6, LDP(X22, X27, X30, POST_INDEXED, -288))
T(0xa8c07927, LDP(X7, X30, X9, POST_INDEXED, 0))
T(0x29de4596, LDP(W22, W17, X12, PRE_INDEXED, 240))
T(0x29f8cee7, LDP(W7, W19, X23, PRE_INDEXED, -60))
T(0xa9cd981b, LDP(X27, X6, X0, PRE_INDEXED, 216))
T(0xa9f73e53, LDP(X19, X15, X18, PRE_INDEXED, -144))
T(0xa9d669ae, LDP(X14, X26, X13, PRE_INDEXED, 352))
T(0x297c5bc9, LDP(W9, W22, X30, -32))
T(0x29564676, LDP(W22, W17, X19, 176))
T(0x29723d75, LDP(W21, W15, X11, -112))
T(0xa946c500, LDP(X0, X17, X8, 104))
T(0xa97cbf1f, LDP(XZR, X15, X24, -56))
T(0xa952210c, LDP(X12, X8, X8, 288))
T(0x68f9e362, LDPSW(X2, X24, X27, POST_INDEXED, -52))
T(0x68cff36c, LDPSW(X12, X28, X27, POST_INDEXED, 124))
T(0x69e6a236, LDPSW(X22, X8, X17, PRE_INDEXED, -204))
T(0x69fa7e8f, LDPSW(X15, XZR, X20, PRE_INDEXED, -48))
T(0x69c0ecf8, LDPSW(X24, X27, X7, PRE_INDEXED, 4))
T(0x697ebeb0, LDPSW(X16, X15, X21, -12))
T(0x697f2aaf, LDPSW(X15, X10, X21, -8))
T(0x69681917, LDPSW(X23, X6, X8, -192))
T(0xb857042d, LDR(W13, X1, POST_INDEXED, -144))
T(0xb85197cf, LDR(W15, X30, POST_INDEXED, -231))
T(0xb84de5c5, LDR(W5, X14, POST_INDEXED, 222))
T(0xf85e049a, LDR(X26, X4, POST_INDEXED, -32))
T(0xf84bd5f3, LDR(X19, X15, POST_INDEXED, 189))
T(0xb85e5c14, LDR(W20, X0, PRE_INDEXED, -27))
T(0xb84f7f9d, LDR(W29, X28, PRE_INDEXED, 247))
T(0xb85afc3e, LDR(W30, X1, PRE_INDEXED, -81))
T(0xf85b7fc3, LDR(X3, X30, PRE_INDEXED, -73))
T(0xf852cd3b, LDR(X27, X9, PRE_INDEXED, -212))
T(0xf85c2d0f, LDR(X15, X8, PRE_INDEXED, -62))
T(0xb940fb87, LDR(W7, X28, 248))
T(0xb96d8f49, LDR(W9, X26, 11660))
T(0xb95633e8, LDR(W8, SP, 5680))
T(0xf95f6c29, LDR(X9, X1, 16088))
T(0xf94a36b7, LDR(X23, X21, 5224))
T(0xf96d8b38, LDR(X24, X25, 23312))
T(0x18e56d56, LDR(W22, -217688))
T(0x181c65d5, LDR(W21, 232632))
T(0x185384ef, LDR(W15, 684188))
T(0x58d694fb, LDR(X27, -339300))
T(0x5874d1e9, LDR(X9, 956988))
T(0x58030db3, LDR(X19, 25012))
T(0xb861d9f7, LDR(W23, X15, W1, SXTW, 2))
T(0xb864f8fa, LDR(W26, X7, X4, SXTX, 2))
T(0xf861da0c, LDR(X12, X16, W1, SXTW, 3))
T(0xf8687a2b, LDR(X11, X17, X8, LSL, 3))
T(0x385a47e7, LDRB(W7, SP, POST_INDEXED, -92))
T(0x3843e560, LDRB(W0, X11, POST_INDEXED, 62))
T(0x3845244b, LDRB(W11, X2, POST_INDEXED, 82))
T(0x38418de0, LDRB(W0, X15, PRE_INDEXED, 24))
T(0x3851ac56, LDRB(W22, X2, PRE_INDEXED, -230))
T(0x38498f45, LDRB(W5, X26, PRE_INDEXED, 152))
T(0x396c81d3, LDRB(W19, X14, 2848))
T(0x394088bd, LDRB(W29, X5, 34))
T(0x3974ea54, LDRB(W20, X18, 3386))
T(0x387b49b1, LDRB(W17, X13, W27, UXTW, 0))
T(0x38706a97, LDRB(W23, X20, X16))
T(0x38756b62, LDRB(W2, X27, X21, LSL, 0))
T(0x38686841, LDRB(W1, X2, X8, LSL, 0))
T(0x7856f77e, LDRH(W30, X27, POST_INDEXED, -145))
T(0x785be40f, LDRH(W15, X0, POST_INDEXED, -66))
T(0x784a043b, LDRH(W27, X1, POST_INDEXED, 160))
T(0x784bbe81, LDRH(W1, X20, PRE_INDEXED, 187))
T(0x7845ec86, LDRH(W6, X4, PRE_INDEXED, 94))
T(0x78512d87, LDRH(W7, X12, PRE_INDEXED, -238))
T(0x797ac1bc, LDRH(W28, X13, 7520))
T(0x797091bc, LDRH(W28, X13, 6216))
T(0x79538523, LDRH(W3, X9, 2498))
T(0x78757a85, LDRH(W5, X20, X21, LSL, 1))
T(0x7878f80a, LDRH(W10, X0, X24, SXTX, 1))
T(0x38d04427, LDRSB(W7, X1, POST_INDEXED, -252))
T(0x38c17465, LDRSB(W5, X3, POST_INDEXED, 23))
T(0x38d1f616, LDRSB(W22, X16, POST_INDEXED, -225))
T(0x3886e710, LDRSB(X16, X24, POST_INDEXED, 110))
T(0x3894258d, LDRSB(X13, X12, POST_INDEXED, -190))
T(0x3882254b, LDRSB(X11, X10, POST_INDEXED, 34))
T(0x38d99eba, LDRSB(W26, X21, PRE_INDEXED, -103))
T(0x38d21f0d, LDRSB(W13, X24, PRE_INDEXED, -223))
T(0x38c75ff4, LDRSB(W20, SP, PRE_INDEXED, 117))
T(0x388e3da0, LDRSB(X0, X13, PRE_INDEXED, 227))
T(0x3886fff8, LDRSB(X24, SP, PRE_INDEXED, 111))
T(0x3882fe8d, LDRSB(X13, X20, PRE_INDEXED, 47))
T(0x39e989ce, LDRSB(W14, X14, 2658))
T(0x39da8524, LDRSB(W4, X9, 1697))
T(0x39e3909d, LDRSB(W29, X4, 2276))
T(0x39ae7a3e, LDRSB(X30, X17, 2974))
T(0x398b4e17, LDRSB(X23, X16, 723))
T(0x398cb244, LDRSB(X4, X18, 812))
T(0x38ede9b6, LDRSB(W22, X13, X13, SXTX))
T(0x38ffe87a, LDRSB(W26, X3, XZR, SXTX))
T(0x38fc698c, LDRSB(W12, X12, X28, LSL, 0))
T(0x38e16b71, LDRSB(W17, X27, X1))
T(0x38f368f0, LDRSB(W16, X7, X19))
T(0x38bce899, LDRSB(X25, X4, X28, SXTX))
T(0x38bf694a, LDRSB(X10, X10, XZR))
T(0x38a869b4, LDRSB(X20, X13, X8, LSL, 0))
T(0x38b86afb, LDRSB(X27, X23, X24, LSL, 0))
T(0x78dc0784, LDRSH(W4, X28, POST_INDEXED, -64))
T(0x78c5f7c7, LDRSH(W7, X30, POST_INDEXED, 95))
T(0x78db3723, LDRSH(W3, X25, POST_INDEXED, -77))
T(0x78810605, LDRSH(X5, X16, POST_INDEXED, 16))
T(0x7884d7e3, LDRSH(X3, SP, POST_INDEXED, 77))
T(0x7893e745, LDRSH(X5, X26, POST_INDEXED, -194))
T(0x78da1d4f, LDRSH(W15, X10, PRE_INDEXED, -95))
T(0x78c47c8b, LDRSH(W11, X4, PRE_INDEXED, 71))
T(0x78c75e77, LDRSH(W23, X19, PRE_INDEXED, 117))
T(0x7896fe3b, LDRSH(X27, X17, PRE_INDEXED, -145))
T(0x78836e99, LDRSH(X25, X20, PRE_INDEXED, 54))
T(0x7890dcc1, LDRSH(X1, X6, PRE_INDEXED, -243))
T(0x79dd82a5, LDRSH(W5, X21, 3776))
T(0x79e451c9, LDRSH(W9, X14, 4648))
T(0x79f3be44, LDRSH(W4, X18, 6622))
T(0x7981cf94, LDRSH(X20, X28, 230))
T(0x79b5a85b, LDRSH(X27, X2, 6868))
T(0x79ad99be, LDRSH(X30, X13, 5836))
T(0x78abf997, LDRSH(X23, X12, X11, SXTX, 1))
T(0x78b4f93d, LDRSH(X29, X9, X20, SXTX, 1))
T(0x78b66a76, LDRSH(X22, X19, X22))
T(0xb891c49f, LDRSW(XZR, X4, POST_INDEXED, -228))
T(0xb88a86cd, LDRSW(X13, X22, POST_INDEXED, 168))
T(0xb88844ee, LDRSW(X14, X7, POST_INDEXED, 132))
T(0xb8824db0, LDRSW(X16, X13, PRE_INDEXED, 36))
T(0xb8998d1e, LDRSW(X30, X8, PRE_INDEXED, -104))
T(0xb8904e5a, LDRSW(X26, X18, PRE_INDEXED, -252))
T(0xb9a69275, LDRSW(X21, X19, 9872))
T(0xb99456ca, LDRSW(X10, X22, 5204))
T(0xb99041d6, LDRSW(X22, X14, 4160))
T(0x98849e3a, LDRSW(X26, -1010748))
T(0x98ae0db5, LDRSW(X21, -671308))
T(0x9845170c, LDRSW(X12, 565984))
T(0xb8bc496b, LDRSW(X11, X11, W28, UXTW))
T(0xb85fc933, LDTR(W19, X9, -4))
T(0xb84e08ce, LDTR(W14, X6, 224))
T(0xb842db08, LDTR(W8, X24, 45))
T(0xf849dbac, LDTR(X12, X29, 157))
T(0xf84aab89, LDTR(X9, X28, 170))
T(0xf84bf83f, LDTR(XZR, X1, 191))
T(0x385ffb66, LDTRB(W6, X27, -1))
T(0x38455a34, LDTRB(W20, X17, 85))
T(0x38433b32, LDTRB(W18, X25, 51))
T(0x785ca9f3, LDTRH(W19, X15, -54))
T(0x7854f9b5, LDTRH(W21, X13, -177))
T(0x7856b8f4, LDTRH(W20, X7, -149))
T(0x38cbea18, LDTRSB(W24, X16, 190))
T(0x38ca88d0, LDTRSB(W16, X6, 168))
T(0x38cc1bd8, LDTRSB(W24, X30, 193))
T(0x389fa9a8, LDTRSB(X8, X13, -6))
T(0x388628f0, LDTRSB(X16, X7, 98))
T(0x3880199d, LDTRSB(X29, X12, 1))
T(0x78d329fc, LDTRSH(W28, X15, -206))
T(0x78d8fb34, LDTRSH(W20, X25, -113))
T(0x7883c8e4, LDTRSH(X4, X7, 60))
T(0x78974a26, LDTRSH(X6, X17, -140))
T(0x7888ba87, LDTRSH(X7, X20, 139))
T(0xb88b88ce, LDTRSW(X14, X6, 184))
T(0xb88e6892, LDTRSW(X18, X4, 230))
T(0xb88cc9b0, LDTRSW(X16, X13, 204))
T(0xb85f601a, LDUR(W26, X0, -10))
T(0xb844e151, LDUR(W17, X10, 78))
T(0xb85f22d1, LDUR(W17, X22, -14))
T(0xf8449218, LDUR(X24, X16, 73))
T(0xf853b150, LDUR(X16, X10, -197))
T(0xf8541206, LDUR(X6, X16, -191))
T(0x384101cf, LDURB(W15, X14, 16))
T(0x385b417b, LDURB(W27, X11, -76))
T(0x385e01f8, LDURB(W24, X15, -32))
T(0x785a21e0, LDURH(W0, X15, -94))
T(0x7856f341, LDURH(W1, X26, -145))
T(0x7856c3cd, LDURH(W13, X30, -148))
T(0x38d5a116, LDURSB(W22, X8, -166))
T(0x38db5091, LDURSB(W17, X4, -75))
T(0x38c731f0, LDURSB(W16, X15, 115))
T(0x389c6359, LDURSB(X25, X26, -58))
T(0x389e321e, LDURSB(X30, X16, -29))
T(0x389ab0e7, LDURSB(X7, X7, -85))
T(0x78de7285, LDURSH(W5, X20, -25))
T(0x78c14227, LDURSH(W7, X17, 20))
T(0x78d6d094, LDURSH(W20, X4, -147))
T(0x788ee161, LDURSH(X1, X11, 238))
T(0x789951b6, LDURSH(X22, X13, -107))
T(0x789ab083, LDURSH(X3, X4, -85))
T(0xb89a4372, LDURSW(X18, X27, -92))
T(0xb88e2029, LDURSW(X9, X1, 226))
T(0xb8896116, LDURSW(X22, X8, 150))
T(0x887f602f, LDXP(W15, W24, X1))
T(0x887f27af, LDXP(W15, W9, X29))
T(0x887f2b3f, LDXP(WZR, W10, X25))
T(0xc87f17fe, LDXP(X30, X5, SP))
T(0xc87f2e82, LDXP(X2, X11, X20))
T(0xc87f3482, LDXP(X2, X13, X4))
T(0x885f7cb5, LDXR(W21, X5))
T(0x885f7d07, LDXR(W7, X8))
T(0x885f7cb2, LDXR(W18, X5))
T(0xc85f7e09, LDXR(X9, X16))
T(0xc85f7ca0, LDXR(X0, X5))
T(0xc85f7ea3, LDXR(X3, X21))
T(0x85f7e1b, LDXRB(W27, X16))
T(0x85f7f20, LDXRB(W0, X25))
T(0x85f7c52, LDXRB(W18, X2))
T(0x485f7c04, LDXRH(W4, X0))
T(0x485f7f5a, LDXRH(W26, X26))
T(0x485f7dad, LDXRH(W13, X13))
T(0xd34f2f99, UBFIZ(X25, X28, 49, 12))
T(0xd37011fb, UBFIZ(X27, X15, 16, 5))
T(0xd37248f9, UBFIZ(X25, X7, 14, 19))
T(0x1ac9220c, LSL(W12, W16, W9))
T(0x1ad723b1, LSL(W17, W29, W23))
T(0x1ad8222c, LSL(W12, W17, W24))
T(0x9ada23a7, LSL(X7, X29, X26))
T(0x9ad72280, LSL(X0, X20, X23))
T(0x9aca212b, LSL(X11, X9, X10))
T(0x1ace21f9, LSLV(W25, W15, W14))
T(0x1ac8202e, LSLV(W14, W1, W8))
T(0x1ac02127, LSLV(W7, W9, W0))
T(0x9ad12327, LSLV(X7, X25, X17))
T(0x9ad12167, LSLV(X7, X11, X17))
T(0x9ac721e9, LSLV(X9, X15, X7))
T(0x53157dee, LSR(W14, W15, 21))
T(0x53117d59, LSR(W25, W10, 17))
T(0xd37cff9a, LSR(X26, X28, 60))
T(0xd36afc35, LSR(X21, X1, 42))
T(0xd37eff6d, LSR(X13, X27, 62))
T(0x1ac32537, LSR(W23, W9, W3))
T(0x1adc268a, LSR(W10, W20, W28))
T(0x1ad027df, LSR(WZR, W30, W16))
T(0x9ac626b5, LSR(X21, X21, X6))
T(0x9acb251d, LSR(X29, X8, X11))
T(0x9ad42493, LSR(X19, X4, X20))
T(0x1acf2548, LSRV(W8, W10, W15))
T(0x1ac826ec, LSRV(W12, W23, W8))
T(0x1ac12589, LSRV(W9, W12, W1))
T(0x9ad22489, LSRV(X9, X4, X18))
T(0x9add25e8, LSRV(X8, X15, X29))
T(0x9ad825f6, LSRV(X22, X15, X24))
T(0x1b185084, MADD(W4, W4, W24, W20))
T(0x1b0422b8, MADD(W24, W21, W4, W8))
T(0x1b0720e1, MADD(W1, W7, W7, W8))
T(0x9b1d2216, MADD(X22, X16, X29, X8))
T(0x9b140a27, MADD(X7, X17, X20, X2))
T(0x9b152674, MADD(X20, X19, X21, X9))
T(0x1b17fc9d, MNEG(W29, W4, W23))
T(0x1b08fd74, MNEG(W20, W11, W8))
T(0x1b1cfd9a, MNEG(W26, W12, W28))
T(0x9b03fd73, MNEG(X19, X11, X3))
T(0x9b0bffca, MNEG(X10, X30, X11))
T(0x9b0bfcd5, MNEG(X21, X6, X11))
// T(0x320693ff, MOV(MOV WSP, 2080406528))
// T(0x320663ea, MOV(ORR W10, WZR, 0xfc07ffff))
// T(0x322417f2, MOV(MOV W18, -268435453))
// T(0xb22dbbf7, MOV(MOV X23, -1125917086973957))
// T(0xb2611ff7, MOV(MOV X23, 547608330240))
// T(0xb249f3f6, MOV(ORR X22, XZR, 0xff8fffffffffffff))
// T(0x12bbdad5, MOV(MOV W21, 556400639))
// T(0x128268c7, MOV(MOV W7, -4935))
// T(0x12bc99eb, MOV(MOV W11, 456196095))
// T(0x92b235cc, MOV(MOV X12, -2444099585))
// T(0x92beced5, MOV(MOV X21, -4134928385))
// T(0x92a00188, MOV(MOV X8, -786433))
T(0x2a1b03f2, MOV(W18, W27))
T(0x2a1603e5, MOV(W5, W22))
T(0x2a0c03ea, MOV(W10, W12))
T(0xaa1a03ec, MOV(X12, X26))
T(0xaa1e03e4, MOV(X4, X30))
T(0xaa1303f6, MOV(X22, X19))
T(0x1100004d, ADD(W13, W2, 0))
T(0x1100004e, ADD(W14, W2, 0))
T(0x1100038f, ADD(W15, W28, 0))
T(0x910001dd, ADD(X29, X14, 0))
T(0x91000054, ADD(X20, X2, 0))
T(0x910001d6, ADD(X22, X14, 0))
// T(0x52911b84, MOV(MOV W4, 35036))
// T(0x529956c5, MOV(MOV W5, 51894))
// T(0x529b9816, MOV(MOV W22, 56512))
// T(0xd2acc067, MOV(MOV X7, 1711472640))
// T(0xd28fcc02, MOV(MOV X2, 32352))
// T(0xd2dbbfce, MOV(MOV X14, 244082991431680))
T(0x72883d49, MOVK(W9, 16874))
T(0x72811bd7, MOVK(W23, 2270))
T(0x72a0d0e1, MOVK(W1, {1671, MovImm16Shift::SHL_16}))
T(0xf2b37940, MOVK(X0, {39882, MovImm16Shift::SHL_16}))
T(0xf2bfb1bf, MOVK(XZR, {64909, MovImm16Shift::SHL_16}))
T(0xf2e780fd, MOVK(X29, {15367, MovImm16Shift::SHL_48}))
// T(0x128b63db, MOVN(MOV W27, -23327))
// T(0x1298ee46, MOVN(MOV W6, -51059))
// T(0x128454b9, MOVN(MOV W25, -8870))
// T(0x9280ff93, MOVN(MOV X19, -2045))
// T(0x92b2768b, MOVN(MOV X11, -2478047233))
// T(0x92f56f4b, MOVN(MOV X11, 6090555546065174527))
T(0x52b62758, MOVZ(W24, 0xB13A0000))
T(0x52b14a9e, MOVZ(W30, 0x8A540000))
T(0x52837eec, MOVZ(W12, 7159))
T(0xd2b5562c, MOVZ(X12, 2863726592))
T(0xd2bbe6f6, MOVZ(X22, 3744923648))
T(0xd2c2e35b, MOVZ(X27, 25400436588544))
// T(0xd533be2b, MRS(X11, S2_3_C11_C14_1))
// T(0xd53029c4, MRS(X4, S2_0_C2_C9_6))
// T(0xd5379580, MRS(X0, S2_7_C9_C5_4))
// T(0xd50640df, MSR(S0_6_C4_C0_6, XZR))
// T(0xd5034a9f, MSR(S0_3_C4_C10_4, XZR))
// T(0xd5074d3f, MSR(S0_7_C4_C13_1, XZR))
// T(0xd5182f8f, MSR(S3_0_C2_C15_4, X15))
// T(0xd511a258, MSR(S2_1_C10_C2_2, X24))
// T(0xd51cdea7, MSR(S3_4_C13_C14_5, X7))
T(0x1b0f962c, MSUB(W12, W17, W15, W5))
T(0x1b01e98d, MSUB(W13, W12, W1, W26))
T(0x1b12c7dd, MSUB(W29, W30, W18, W17))
T(0x9b088341, MSUB(X1, X26, X8, X0))
T(0x9b19e742, MSUB(X2, X26, X25, X25))
T(0x9b12c009, MSUB(X9, X0, X18, X16))
T(0x1b007e8f, MUL(W15, W20, W0))
T(0x1b087dad, MUL(W13, W13, W8))
T(0x1b1b7ebc, MUL(W28, W21, W27))
T(0x9b097ce8, MUL(X8, X7, X9))
T(0x9b1f7e8f, MUL(X15, X20, XZR))
T(0x9b107dc0, MUL(X0, X14, X16))
T(0x2a3c27ec, MVN(W12, W28, LSL, 9))
T(0xaa2bbbff, MVN(XZR, X11, LSL, 46))
T(0xaa7abff4, MVN(X20, X26, LSR, 47))
T(0xaa786beb, MVN(X11, X24, LSR, 26))
T(0x4b9643e6, NEG(W6, W22, ASR, 16))
T(0xcb8acffc, NEG(X28, X10, ASR, 51))
T(0xcb5a97fc, NEG(X28, X26, LSR, 37))
T(0x6b562be9, NEGS(W9, W22, LSR, 10))
T(0x6b9177f3, NEGS(W19, W17, ASR, 29))
T(0xeb9ca3eb, NEGS(X11, X28, ASR, 40))
T(0xeb407beb, NEGS(X11, X0, LSR, 30))
T(0xeb878be5, NEGS(X5, X7, ASR, 34))
T(0x5a0f03e7, NGC(W7, W15))
T(0x5a0103ed, NGC(W13, W1))
T(0x5a0803e9, NGC(W9, W8))
T(0xda1a03fa, NGC(X26, X26))
T(0xda1503eb, NGC(X11, X21))
T(0xda1a03f9, NGC(X25, X26))
T(0x7a0203e2, NGCS(W2, W2))
T(0x7a0003e0, NGCS(W0, W0))
T(0x7a0a03f0, NGCS(W16, W10))
T(0xfa1f03e1, NGCS(X1, XZR))
T(0xfa1603f1, NGCS(X17, X22))
T(0xfa1703f4, NGCS(X20, X23))
T(0xd503201f, NOP())
T(0xaae78e17, ORN(X23, X16, X7, ROR, 35))
T(0xaa7990e9, ORN(X9, X7, X25, LSR, 36))
T(0xaaefa800, ORN(X0, X0, X15, ROR, 42))
T(0x32009ac2, ORR(W2, W22, 0x7f007f))
T(0x32066167, ORR(W7, W11, 0xfc07ffff))
T(0x32032ab2, ORR(W18, W21, 0xe00000ff))
T(0xb25c9b18, ORR(X24, X24, 0xfffffff0000007ff))
T(0xb205b7ce, ORR(X14, X30, 0xf9fff9fff9fff9ff))
T(0xb24d4190, ORR(X16, X12, 0xfff800000000000f))
T(0x2add28ac, ORR(W12, W5, W29, ROR, 10))
T(0x2a556110, ORR(W16, W8, W21, LSR, 24))
T(0xaa977e5e, ORR(X30, X18, X23, ASR, 31))
T(0xaa949fd6, ORR(X22, X30, X20, ASR, 39))
T(0xaa918fd6, ORR(X22, X30, X17, ASR, 35))
// T(0xf98f5696, PRFM(22, [X20, 7848]))
// T(0xf9ae98c3, PRFM(PLDL2STRM, [X6, 23856]))
// T(0xf983faf6, PRFM(22, [X23, 2032]))
// T(0xd8062b53, PRFM(PSTL2STRM, 50536))
// T(0xd88f84ae, PRFM(14, -921452))
// T(0xd8b42918, PRFM(24, -621280))
// T(0xf8b4fa1b, PRFM(27, [X16, X20, SXTX 3]))
// T(0xf894b04a, PRFUM(PLIL2KEEP, [X2, -181]))
// T(0xf88090f8, PRFUM(24, [X7, 9]))
// T(0xf881126c, PRFUM(PLIL3KEEP, [X19, 17]))
T(0xd503349f, PSSBB())
T(0x5ac00290, RBIT(W16, W20))
T(0x5ac000b4, RBIT(W20, W5))
T(0x5ac0031c, RBIT(W28, W24))
T(0xdac003ee, RBIT(X14, XZR))
T(0xdac000bb, RBIT(X27, X5))
T(0xdac0003a, RBIT(X26, X1))
T(0xd65f0220, RET(X17))
T(0xd65f0080, RET(X4))
T(0xd65f0300, RET(X24))
T(0x5ac00940, REV(W0, W10))
T(0x5ac00931, REV(W17, W9))
T(0x5ac00a6e, REV(W14, W19))
T(0xdac00dfc, REV(X28, X15))
T(0xdac00e46, REV(X6, X18))
T(0xdac00fef, REV(X15, XZR))
T(0x5ac0057c, REV16(W28, W11))
T(0x5ac0042a, REV16(W10, W1))
T(0x5ac005f8, REV16(W24, W15))
T(0xdac00687, REV16(X7, X20))
T(0xdac0046b, REV16(X11, X3))
T(0xdac00774, REV16(X20, X27))
T(0xdac009e5, REV32(X5, X15))
T(0xdac00b37, REV32(X23, X25))
T(0xdac0098b, REV32(X11, X12))
T(0xdac00ccc, REV64(X12, X6))
T(0xdac00d1c, REV64(X28, X8))
T(0xdac00e07, REV64(X7, X16))
T(0x138f39e1, ROR(W1, W15, 14))
T(0x13836129, EXTR(W9, W9, W3, 24))
T(0x1399195c, EXTR(W28, W10, W25, 6))
T(0x93d42677, EXTR(X23, X19, X20, 9))
T(0x93c7673d, EXTR(X29, X25, X7, 25))
T(0x93d30b75, EXTR(X21, X27, X19, 2))
T(0x1ad22e2c, ROR(W12, W17, W18))
T(0x1ad32f22, ROR(W2, W25, W19))
T(0x1ac92fb4, ROR(W20, W29, W9))
T(0x9ad62c41, ROR(X1, X2, X22))
T(0x9ac82ec8, ROR(X8, X22, X8))
T(0x9ad02caf, ROR(X15, X5, X16))
T(0x1ad72c7b, RORV(W27, W3, W23))
T(0x1ac32e89, RORV(W9, W20, W3))
T(0x1ad72f7f, RORV(WZR, W27, W23))
T(0x9ad02d91, RORV(X17, X12, X16))
T(0x9ac12c0f, RORV(X15, X0, X1))
T(0x9ac82e05, RORV(X5, X16, X8))
// T(0xd50330ff, SB(MSR S0_3_C3_C0_7, XZR))
// T(0xd50330ff, SB(MSR S0_3_C3_C0_7, XZR))
// T(0xd50330ff, SB(MSR S0_3_C3_C0_7, XZR))
T(0x5a1103d2, SBC(W18, W30, W17))
T(0x5a14037a, SBC(W26, W27, W20))
T(0x5a1e01b6, SBC(W22, W13, W30))
T(0xda07039a, SBC(X26, X28, X7))
T(0xda000302, SBC(X2, X24, X0))
T(0xda040328, SBC(X8, X25, X4))
T(0x7a150381, SBCS(W1, W28, W21))
T(0x7a0c00b4, SBCS(W20, W5, W12))
T(0x7a0901ff, SBCS(WZR, W15, W9))
T(0xfa1300cd, SBCS(X13, X6, X19))
T(0xfa0803b2, SBCS(X18, X29, X8))
T(0xfa0a02db, SBCS(X27, X22, X10))
T(0x13071e06, SBFX(W6, W16, 7, 1))
T(0x935dec9e, SBFX(X30, X4, 29, 31))
T(0x93425b46, SBFX(X6, X26, 2, 21))
T(0x937b1839, SBFIZ(X25, X1, 5, 7))
T(0x934fc2e5, SBFX(X5, X23, 15, 34))
T(0x937c9c6a, SBFIZ(X10, X3, 4, 40))
T(0x9342c6d5, SBFX(X21, X22, 2, 48))
T(0x937bdfaf, SBFIZ(X15, X29, 5, 56))
T(0x934cc132, SBFX(X18, X9, 12, 37))
T(0x93532a0d, SBFIZ(X13, X16, 45, 11))
T(0x1ac40e5c, SDIV(W28, W18, W4))
T(0x1ac90d95, SDIV(W21, W12, W9))
T(0x1ad90da9, SDIV(W9, W13, W25))
T(0x9ad70d33, SDIV(X19, X9, X23))
T(0x9ad80e4f, SDIV(X15, X18, X24))
T(0x9ada0fc1, SDIV(X1, X30, X26))
T(0xd503209f, SEV())
T(0xd50320bf, SEVL())
T(0x9b263e23, SMADDL(X3, W17, W6, X15))
T(0x9b3b6702, SMADDL(X2, W24, W27, X25))
T(0x9b3365e9, SMADDL(X9, W15, W19, X25))
T(0x9b36ff09, SMNEGL(X9, W24, W22))
T(0x9b39fcb4, SMNEGL(X20, W5, W25))
T(0x9b34ff4a, SMNEGL(X10, W26, W20))
T(0x9b3cd6be, SMSUBL(X30, W21, W28, X21))
T(0x9b38a754, SMSUBL(X20, W26, W24, X9))
T(0x9b20986b, SMSUBL(X11, W3, W0, X6))
T(0x9b527d97, SMULH(X23, X12, X18))
T(0x9b4f7f85, SMULH(X5, X28, X15))
T(0x9b437caf, SMULH(X15, X5, X3))
T(0x9b247cb0, SMULL(X16, W5, W4))
T(0x9b217e14, SMULL(X20, W16, W1))
T(0x9b327d93, SMULL(X19, W12, W18))
T(0xd503309f, SSBB())
T(0x889ffcb9, STLR(W25, X5))
T(0x889fff50, STLR(W16, X26))
T(0x889fffd4, STLR(W20, X30))
T(0xc89fff60, STLR(X0, X27))
T(0xc89fff1b, STLR(X27, X24))
T(0xc89fff6f, STLR(X15, X27))
T(0x89ffd4e, STLRB(W14, X10))
T(0x89ffc3a, STLRB(W26, X1))
T(0x89fff47, STLRB(W7, X26))
T(0x489ffd4e, STLRH(W14, X10))
T(0x489fff75, STLRH(W21, X27))
T(0x489ffe16, STLRH(W22, X16))
T(0x8820bf9b, STLXP(W0, W27, W15, X28))
T(0x8824811a, STLXP(W4, W26, W0, X8))
T(0x8835c975, STLXP(W21, W21, W18, X11))
T(0xc82d8ef0, STLXP(W13, X16, X3, X23))
T(0xc8209aa0, STLXP(W0, X0, X6, X21))
T(0xc83d8a2c, STLXP(W29, X12, X2, X17))
T(0x881afc2b, STLXR(W26, W11, X1))
T(0x881efe87, STLXR(W30, W7, X20))
T(0x8807fcb4, STLXR(W7, W20, X5))
T(0xc807fd09, STLXR(W7, X9, X8))
T(0xc815ffef, STLXR(W21, X15, SP))
T(0xc809ffb4, STLXR(W9, X20, X29))
T(0x0814ff76, STLXRB(W20, W22, X27))
T(0x0814fff8, STLXRB(W20, W24, SP))
T(0x0800fcec, STLXRB(W0, W12, X7))
T(0x4812fd16, STLXRH(W18, W22, X8))
T(0x4805fc10, STLXRH(W5, W16, X0))
T(0x4808fda8, STLXRH(W8, W8, X13))
T(0x2835e639, STNP(W25, W25, X17, -84))
T(0x2803a64e, STNP(W14, W9, X18, 28))
T(0x282d688d, STNP(W13, W26, X4, -152))
T(0xa80069bb, STNP(X27, X26, X13))
T(0xa8211567, STNP(X7, X5, X11, -496))
T(0xa83abaea, STNP(X10, X14, X23, -88))
T(0x28977de9, STP(W9, WZR, X15, POST_INDEXED, 184))
T(0x28ab5ae0, STP(W0, W22, X23, POST_INDEXED, -168))
T(0xa8803598, STP(X24, X13, X12, POST_INDEXED, 0))
T(0xa8a15609, STP(X9, X21, X16, POST_INDEXED, -496))
T(0x29a80797, STP(W23, W1, X28, PRE_INDEXED, -192))
T(0x29b84e42, STP(W2, W19, X18, PRE_INDEXED, -64))
T(0x2987acc3, STP(W3, W11, X6, PRE_INDEXED, 60))
T(0xa99a78fa, STP(X26, X30, X7, PRE_INDEXED, 416))
T(0xa9abf070, STP(X16, X28, X3, PRE_INDEXED, -328))
T(0xa9bb7b8d, STP(X13, X30, X28, PRE_INDEXED, -80))
T(0x293a6611, STP(W17, W25, X16, -48))
T(0x293bdc3e, STP(W30, W23, X1, -36))
T(0x292791a2, STP(W2, W4, X13, -196))
T(0xa922272d, STP(X13, X9, X25, -480))
T(0xa9222d40, STP(X0, X11, X10, -480))
T(0xa90017ca, STP(X10, X5, X30))
T(0xb8168414, STR(W20, X0, POST_INDEXED, -152))
T(0xb8090760, STR(W0, X27, POST_INDEXED, 144))
T(0xb81457d6, STR(W22, X30, POST_INDEXED, -187))
T(0xf80e8585, STR(X5, X12, POST_INDEXED, 232))
T(0xf81907f9, STR(X25, SP, POST_INDEXED, -112))
T(0xf806956a, STR(X10, X11, POST_INDEXED, 105))
T(0xb809ceba, STR(W26, X21, PRE_INDEXED, 156))
T(0xb8073f34, STR(W20, X25, PRE_INDEXED, 115))
T(0xb8087f73, STR(W19, X27, PRE_INDEXED, 135))
T(0xf81c4c08, STR(X8, X0, PRE_INDEXED, -60))
T(0xf80fde06, STR(X6, X16, PRE_INDEXED, 253))
T(0xf80a8c2d, STR(X13, X1, PRE_INDEXED, 168))
T(0xb91ab6cb, STR(W11, X22, 6836))
T(0xb9223f0c, STR(W12, X24, 8764))
T(0xb902a76c, STR(W12, X27, 676))
T(0xf9025f66, STR(X6, X27, 1208))
T(0xf90ada92, STR(X18, X20, 5552))
T(0xf92357b6, STR(X22, X29, 18088))
T(0xb82d79d8, STR(W24, X14, X13, LSL, 2))
T(0xb83efa44, STR(W4, X18, X30, SXTX, 2))
T(0xf835e97d, STR(X29, X11, X21, SXTX))
T(0xf82fdb13, STR(X19, X24, W15, SXTW, 3))
T(0x380e7738, STRB(W24, X25, POST_INDEXED, 231))
T(0x381147c4, STRB(W4, X30, POST_INDEXED, -236))
T(0x381c563f, STRB(WZR, X17, POST_INDEXED, -59))
T(0x38169e52, STRB(W18, X18, PRE_INDEXED, -151))
T(0x380f2c10, STRB(W16, X0, PRE_INDEXED, 242))
T(0x3810bf4d, STRB(W13, X26, PRE_INDEXED, -245))
T(0x391137e7, STRB(W7, SP, 1101))
T(0x39277391, STRB(W17, X28, 2524))
T(0x390f6812, STRB(W18, X0, 986))
T(0x383f4922, STRB(W2, X9, WZR, UXTW))
T(0x38244a8b, STRB(W11, X20, W4, UXTW))
T(0x382c6ab6, STRB(W22, X21, X12, LSL, 0))
T(0x382d6957, STRB(W23, X10, X13, LSL, 0))
T(0x38246a7d, STRB(W29, X19, X4, LSL, 0))
T(0x7806b5fd, STRH(W29, X15, POST_INDEXED, 107))
T(0x7801b51e, STRH(W30, X8, POST_INDEXED, 27))
T(0x780f25eb, STRH(W11, X15, POST_INDEXED, 242))
T(0x78080d42, STRH(W2, X10, PRE_INDEXED, 128))
T(0x78149fca, STRH(W10, X30, PRE_INDEXED, -183))
T(0x7815cd49, STRH(W9, X10, PRE_INDEXED, -164))
T(0x790fcf9a, STRH(W26, X28, 2022))
T(0x790bc41f, STRH(WZR, X0, 1506))
T(0x792f6fb7, STRH(W23, X29, 6070))
T(0x78207b81, STRH(W1, X28, X0, LSL, 1))
T(0xb81f79bd, STTR(W29, X13, -9))
T(0xb80cba88, STTR(W8, X20, 203))
T(0xb80aa968, STTR(W8, X11, 170))
T(0xf8036b2e, STTR(X14, X25, 54))
T(0xf81bc87b, STTR(X27, X3, -68))
T(0xf80c4969, STTR(X9, X11, 196))
T(0x380eda7c, STTRB(W28, X19, 237))
T(0x380eba25, STTRB(W5, X17, 235))
T(0x38135bac, STTRB(W12, X29, -203))
T(0x78021bdc, STTRH(W28, X30, 33))
T(0x78070930, STTRH(W16, X9, 112))
T(0x7802fa0f, STTRH(W15, X16, 47))
T(0xb812733d, STUR(W29, X25, -217))
T(0xb811c2d9, STUR(W25, X22, -228))
T(0xb819d3f0, STUR(W16, SP, -99))
T(0xf801828a, STUR(X10, X20, 24))
T(0xf81b9302, STUR(X2, X24, -71))
T(0xf802a27b, STUR(X27, X19, 42))
T(0x3817e34b, STURB(W11, X26, -130))
T(0x380db1f8, STURB(W24, X15, 219))
T(0x381db266, STURB(W6, X19, -37))
T(0x7800639c, STURH(W28, X28, 6))
T(0x780b72a9, STURH(W9, X21, 183))
T(0x7813b3d6, STURH(W22, X30, -197))
T(0x883e27bb, STXP(W30, W27, W9, X29))
T(0x883c089b, STXP(W28, W27, W2, X4))
T(0x883f4a30, STXP(WZR, W16, W18, X17))
T(0xc8332606, STXP(W19, X6, X9, X16))
T(0xc82f7009, STXP(W15, X9, X28, X0))
T(0xc8213ca3, STXP(W1, X3, X15, X5))
T(0x88137e6d, STXR(W19, W13, X19))
T(0x88117eeb, STXR(W17, W11, X23))
T(0x88157c63, STXR(W21, W3, X3))
T(0xc81b7fb4, STXR(W27, X20, X29))
T(0xc8157e0f, STXR(W21, X15, X16))
T(0xc8017e07, STXR(W1, X7, X16))
T(0x081a7dc7, STXRB(W26, W7, X14))
T(0x08187fd4, STXRB(W24, W20, X30))
T(0x08147d80, STXRB(W20, W0, X12))
T(0x48117d88, STXRH(W17, W8, X12))
T(0x480f7eb4, STXRH(W15, W20, X21))
T(0x480c7e17, STXRH(W12, W23, X16))
T(0x4b274cbd, SUB(W29, W5, W7, UXTW, 3))
T(0x4b2ab198, SUB(W24, W12, W10, SXTH, 4))
T(0xcb2c4698, SUB(X24, X20, W12, UXTW, 1))
T(0x51794359, SUB(W25, W26, 3664, LSL, 12))
T(0x5133a211, SUB(W17, W16, 3304))
T(0x510948af, SUB(W15, W5, 594))
T(0xd1088b3e, SUB(X30, X25, 546))
T(0xd10478ac, SUB(X12, X5, 286))
T(0xd12e98b0, SUB(X16, X5, 2982))
T(0x4b8b06d7, SUB(W23, W22, W11, ASR, 1))
T(0xcb4adab1, SUB(X17, X21, X10, LSR, 54))
T(0xcb9656de, SUB(X30, X22, X22, ASR, 21))
T(0x6b3d43b0, SUBS(W16, W29, W29, UXTW))
T(0x6b3d48a8, SUBS(W8, W5, W29, UXTW, 2))
T(0x71151784, SUBS(W4, W28, 1349))
T(0x711256f6, SUBS(W22, W23, 1173))
T(0x7177bf09, SUBS(W9, W24, 3567, LSL, 12))
T(0xf14780b4, SUBS(X20, X5, 480, LSL, 12))
T(0xf148fefb, SUBS(X27, X23, 575, LSL, 12))
T(0xf146e7f4, SUBS(X20, SP, 441, LSL, 12))
T(0x6b1031f2, SUBS(W18, W15, W16, LSL, 12))
T(0x6b8333ce, SUBS(W14, W30, W3, ASR, 12))
T(0xeb53b67e, SUBS(X30, X19, X19, LSR, 45))
T(0xeb0b1ecb, SUBS(X11, X22, X11, LSL, 7))
T(0xeb89fbaa, SUBS(X10, X29, X9, ASR, 62))
T(0xd4189fe1, SVC(0xc4ff))
T(0xd4150361, SVC(0xa81b))
T(0xd4076cc1, SVC(0x3b66))
T(0x13001fd4, SXTB(W20, W30))
T(0x13001f06, SXTB(W6, W24))
T(0x13001d79, SXTB(W25, W11))
T(0x93401dd3, SXTB(X19, W14))
T(0x93401c21, SXTB(X1, W1))
T(0x93401c3f, SXTB(XZR, W1))
T(0x13003cdb, SXTH(W27, W6))
T(0x13003efe, SXTH(W30, W23))
T(0x13003e8a, SXTH(W10, W20))
T(0x93403f2a, SXTH(X10, W25))
T(0x93403dcf, SXTH(X15, W14))
T(0x93403c86, SXTH(X6, W4))
T(0x93407f3e, SXTW(X30, W25))
T(0x93407ff0, SXTW(X16, WZR))
T(0x93407c33, SXTW(X19, W1))
// T(0xd50c191e, SYS(4, C1, C9, 0, X30))
// T(0xd50b81cc, SYS(3, C8, C1, 6, X12))
// T(0xd508c696, SYS(0, C12, C6, 4, X22))
// T(0xd5295947, SYSL(X7, 1, C5, C9, 2))
// T(0xd52e3309, SYSL(X9, 6, C3, C3, 0))
// T(0xd52ed8e8, SYSL(X8, 6, C13, C8, 7))
T(0x375080a2, TBNZ(W2, 10, 4116))
T(0x370be7cf, TBNZ(W15, 1, 31992))
T(0x3767c975, TBNZ(W21, 12, -1748))
T(0x36f2fd0d, TBZ(W13, 30, 24480))
T(0x360d605e, TBZ(W30, 1, -21496))
T(0xb68a3a8a, TBZ(X10, 49, 18256))
// T(0xd50a891b, TLBI(SYS 2, C8, C9, 0, X27))
// T(0xd50e84aa, TLBI(SYS 6, C8, C4, 5, X10))
// T(0xd50b8326, TLBI(SYS 3, C8, C3, 1, X6))
T(0x720291df, TST(W14, 0xc007c007))
T(0x7200475f, TST(W26, 0x3ffff))
T(0x7202e85f, TST(W2, 0xdddddddd))
T(0xf20550ff, TST(X7, 0xf800fffff800ffff))
T(0xf27cee1f, TST(X16, 0xfffffffffffffff0))
T(0xf25ec0ff, TST(X7, 0xfffffffc0007ffff))
T(0x6ac3053f, TST(W9, W3, ROR, 1))
T(0xea492dbf, TST(X13, X9, LSR, 11))
T(0xea14741f, TST(X0, X20, LSL, 29))
T(0xeacab8df, TST(X6, X10, ROR, 46))
T(0x53163c68, UBFIZ(W8, W3, 10, 16))
T(0xd341ffc7, LSR(X7, X30, 1))
T(0xd3488e5d, UBFX(X29, X18, 8, 28))
T(0xd350a352, UBFX(X18, X26, 16, 25))
T(0x531e2975, UBFIZ(W21, W11, 2, 11))
T(0xd35cd8fe, UBFX(X30, X7, 28, 27))
T(0xd34a1ac5, UBFIZ(X5, X22, 54, 7))
T(0xd349058e, UBFIZ(X14, X12, 55, 2))
T(0xd34fcc95, UBFX(X21, X4, 15, 37))
T(0xd36f46a6, UBFIZ(X6, X21, 17, 18))
T(0xd34d7391, UBFX(X17, X28, 13, 16))
T(0x0000d975, UDF(55669))
T(0x00000de0, UDF(3552))
T(0x0000743c, UDF(29756))
T(0x1adf0ae2, UDIV(W2, W23, WZR))
T(0x1ad60938, UDIV(W24, W9, W22))
T(0x1acc0a9a, UDIV(W26, W20, W12))
T(0x9ad20927, UDIV(X7, X9, X18))
T(0x9adc08da, UDIV(X26, X6, X28))
T(0x9ace08ea, UDIV(X10, X7, X14))
T(0x9ba85eac, UMADDL(X12, W21, W8, X23))
T(0x9baf6c1b, UMADDL(X27, W0, W15, X27))
T(0x9baa0e23, UMADDL(X3, W17, W10, X3))
T(0x9ba2ff0b, UMNEGL(X11, W24, W2))
T(0x9bb6fe3f, UMNEGL(XZR, W17, W22))
T(0x9bb7fe5d, UMNEGL(X29, W18, W23))
T(0x9bb7bfa4, UMSUBL(X4, W29, W23, X15))
T(0x9bbb81ba, UMSUBL(X26, W13, W27, X0))
T(0x9bb38c6d, UMSUBL(X13, W3, W19, X3))
T(0x9bdb7c70, UMULH(X16, X3, X27))
T(0x9bc47f0f, UMULH(X15, X24, X4))
T(0x9bcf7c4b, UMULH(X11, X2, X15))
T(0x9ba77ddd, UMULL(X29, W14, W7))
T(0x9bad7e73, UMULL(X19, W19, W13))
T(0x9ba27fd0, UMULL(X16, W30, W2))
T(0x53001db9, UXTB(W25, W13))
T(0x53001cd1, UXTB(W17, W6))
T(0x53001e24, UXTB(W4, W17))
T(0x53003ed1, UXTH(W17, W22))
T(0x53003de4, UXTH(W4, W15))
T(0x53003ce2, UXTH(W2, W7))
T(0xd503205f, WFE())
T(0xd503207f, WFI())
T(0xd503203f, YIELD())
