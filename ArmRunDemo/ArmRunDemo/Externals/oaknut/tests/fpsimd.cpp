// SPDX-FileCopyrightText: Copyright (c) 2022 merryhime <https://mary.rs>
// SPDX-License-Identifier: MIT

#include <array>
#include <cstdint>

#include <catch2/catch_test_macros.hpp>

#include "oaknut/oaknut.hpp"

#define T(HEX, CMD)                   \
    TEST_CASE(#CMD)                   \
    {                                 \
        using namespace oaknut;       \
        using namespace oaknut::util; \
                                      \
        std::uint32_t result;         \
        CodeGenerator code{&result};  \
                                      \
        code.CMD;                     \
                                      \
        REQUIRE(result == HEX);       \
    }

T(0x5ee0bb61, ABS(D1, D27))
T(0x4e20ba03, ABS(V3.B16(), V16.B16()))
T(0x5ef98449, ADD(D9, D2, D25))
T(0x4eef8697, ADD(V23.D2(), V20.D2(), V15.D2()))
T(0x0eb743d1, ADDHN(V17.S2(), V30.D2(), V23.D2()))
T(0x5ef1b933, ADDP(D19, V9.D2()))
T(0x0e7ebf6e, ADDP(V14.H4(), V27.H4(), V30.H4()))
T(0x4e31ba47, ADDV(B7, V18.B16()))
// AESD
// AESE
// AESIMC
// AESMC
T(0x4e2b1d4c, AND(V12.B16(), V10.B16(), V11.B16()))
T(0x6f01b7f4, BIC(V20.H8(), 63, LSL, 8))
T(0x2f017752, BIC(V18.S2(), 58, LSL, 24))
T(0x0e751c85, BIC(V5.B8(), V4.B8(), V21.B8()))
T(0x2ef11d4d, BIF(V13.B8(), V10.B8(), V17.B8()))
T(0x2eb31f3b, BIT(V27.B8(), V25.B8(), V19.B8()))
T(0x2e711ed8, BSL(V24.B8(), V22.B8(), V17.B8()))
T(0x0e604aaf, CLS(V15.H4(), V21.H4()))
T(0x6e604808, CLZ(V8.H8(), V0.H8()))
T(0x7eff8ec2, CMEQ(D2, D22, D31))
T(0x2e2b8d57, CMEQ(V23.B8(), V10.B8(), V11.B8()))
T(0x5ee09bbf, CMEQ(D31, D29, 0))
T(0x4ea09876, CMEQ(V22.S4(), V3.S4(), 0))
T(0x5ef23c04, CMGE(D4, D0, D18))
T(0x4e203c6f, CMGE(V15.B16(), V3.B16(), V0.B16()))
T(0x7ee08822, CMGE(D2, D1, 0))
T(0x2ea08bb9, CMGE(V25.S2(), V29.S2(), 0))
T(0x5ef036a5, CMGT(D5, D21, D16))
T(0x0eb7358b, CMGT(V11.S2(), V12.S2(), V23.S2()))
T(0x5ee08957, CMGT(D23, D10, 0))
T(0x4ea088eb, CMGT(V11.S4(), V7.S4(), 0))
T(0x7ee235f2, CMHI(D18, D15, D2))
T(0x6e243596, CMHI(V22.B16(), V12.B16(), V4.B16()))
T(0x7ef23faf, CMHS(D15, D29, D18))
T(0x2e2d3d8a, CMHS(V10.B8(), V12.B8(), V13.B8()))
T(0x7ee098a4, CMLE(D4, D5, 0))
T(0x2e2098d3, CMLE(V19.B8(), V6.B8(), 0))
T(0x5ee0a980, CMLT(D0, D12, 0))
T(0x4e60a892, CMLT(V18.H8(), V4.H8(), 0))
T(0x5ee18e03, CMTST(D3, D16, D1))
T(0x4e708f65, CMTST(V5.H8(), V27.H8(), V16.H8()))
T(0x4e20598d, CNT(V13.B16(), V12.B16()))
// DUP
T(0x0e0d06cd, DUP(V13.B8(), V22.B()[6]))
T(0x0e010fe7, DUP(V7.B8(), WZR))
T(0x2e2b1e6e, EOR(V14.B8(), V19.B8(), V11.B8()))
T(0x6e1c0a35, EXT(V21.B16(), V17.B16(), V28.B16(), 1))
T(0x7ea0d7a4, FABD(S4, S29, S0))
T(0x6eecd418, FABD(V24.D2(), V0.D2(), V12.D2()))
T(0x1e20c299, FABS(S25, S20))
T(0x1e60c114, FABS(D20, D8))
T(0x4ee0f999, FABS(V25.D2(), V12.D2()))
T(0x7e71ef5b, FACGE(D27, D26, D17))
T(0x6e6eed17, FACGE(V23.D2(), V8.D2(), V14.D2()))
T(0x7ef8efc0, FACGT(D0, D30, D24))
T(0x6eb0ec54, FACGT(V20.S4(), V2.S4(), V16.S4()))
T(0x1e242b23, FADD(S3, S25, S4))
T(0x1e672a8b, FADD(D11, D20, D7))
T(0x4e34d46b, FADD(V11.S4(), V3.S4(), V20.S4()))
T(0x7e30db16, FADDP(S22, V24.S2()))
T(0x6e21d626, FADDP(V6.S4(), V17.S4(), V1.S4()))
T(0x1e23c4ce, FCCMP(S6, S3, 14, GT))
T(0x1e7104aa, FCCMP(D5, D17, 10, EQ))
T(0x1e28c678, FCCMPE(S19, S8, 8, GT))
T(0x1e6195dd, FCCMPE(D14, D1, 13, LS))
T(0x5e31e659, FCMEQ(S25, S18, S17))
T(0x0e27e7ba, FCMEQ(V26.S2(), V29.S2(), V7.S2()))
T(0x5ea0da1d, FCMEQ(S29, S16, 0.0))
T(0x4ee0db8a, FCMEQ(V10.D2(), V28.D2(), 0.0))
T(0x7e2de473, FCMGE(S19, S3, S13))
T(0x2e33e726, FCMGE(V6.S2(), V25.S2(), V19.S2()))
T(0x7ea0c8d3, FCMGE(S19, S6, 0.0))
T(0x6ea0ca7b, FCMGE(V27.S4(), V19.S4(), 0.0))
T(0x7eb7e65d, FCMGT(S29, S18, S23))
T(0x6ef0e6ac, FCMGT(V12.D2(), V21.D2(), V16.D2()))
T(0x5ee0cb5a, FCMGT(D26, D26, 0.0))
T(0x4ea0c917, FCMGT(V23.S4(), V8.S4(), 0.0))
T(0x7ea0dbe1, FCMLE(S1, S31, 0.0))
T(0x6ea0da69, FCMLE(V9.S4(), V19.S4(), 0.0))
T(0x5ea0ea5f, FCMLT(S31, S18, 0.0))
T(0x4ee0e8de, FCMLT(V30.D2(), V6.D2(), 0.0))
T(0x1e322040, FCMP(S2, S18))
T(0x1e202248, FCMP(S18, 0.0))
T(0x1e6520a0, FCMP(D5, D5))
T(0x1e602108, FCMP(D8, 0.0))
T(0x1e332370, FCMPE(S27, S19))
T(0x1e202018, FCMPE(S0, 0.0))
T(0x1e7120b0, FCMPE(D5, D17))
T(0x1e602298, FCMPE(D20, 0.0))
T(0x1e32ed68, FCSEL(S8, S11, S18, AL))
T(0x1e7b1e21, FCSEL(D1, D17, D27, NE))
T(0x1ee24022, FCVT(S2, H1))
T(0x1ee2c33c, FCVT(D28, H25))
T(0x1e23c379, FCVT(H25, S27))
T(0x1e22c1a5, FCVT(D5, S13))
T(0x1e63c2b1, FCVT(H17, D21))
T(0x1e624309, FCVT(S9, D24))
T(0x1e2400a0, FCVTAS(W0, S5))
T(0x9e24000e, FCVTAS(X14, S0))
T(0x1e640191, FCVTAS(W17, D12))
T(0x9e6403d6, FCVTAS(X22, D30))
T(0x5e21c8a7, FCVTAS(S7, S5))
T(0x0e21c8df, FCVTAS(V31.S2(), V6.S2()))
T(0x1e25036b, FCVTAU(W11, S27))
T(0x9e25030c, FCVTAU(X12, S24))
T(0x1e65002e, FCVTAU(W14, D1))
T(0x9e65003e, FCVTAU(X30, D1))
T(0x7e61cabd, FCVTAU(D29, D21))
T(0x2e21c880, FCVTAU(V0.S2(), V4.S2()))
T(0x4e217b66, FCVTL2(V6.S4(), V27.H8()))
T(0x1e30016d, FCVTMS(W13, S11))
T(0x9e3002b5, FCVTMS(X21, S21))
T(0x1e7003dd, FCVTMS(W29, D30))
T(0x9e700080, FCVTMS(X0, D4))
T(0x5e21b9b6, FCVTMS(S22, S13))
T(0x4e61ba4e, FCVTMS(V14.D2(), V18.D2()))
T(0x1e31002d, FCVTMU(W13, S1))
T(0x9e310281, FCVTMU(X1, S20))
T(0x1e71000e, FCVTMU(W14, D0))
T(0x9e710010, FCVTMU(X16, D0))
T(0x7e61bb3b, FCVTMU(D27, D25))
T(0x2e21b918, FCVTMU(V24.S2(), V8.S2()))
T(0x0e616a68, FCVTN(V8.S2(), V19.D2()))
T(0x1e200100, FCVTNS(W0, S8))
T(0x9e20037f, FCVTNS(XZR, S27))
T(0x1e60015e, FCVTNS(W30, D10))
T(0x9e600018, FCVTNS(X24, D0))
T(0x5e61a846, FCVTNS(D6, D2))
T(0x4e21aa81, FCVTNS(V1.S4(), V20.S4()))
T(0x1e210248, FCVTNU(W8, S18))
T(0x9e2103da, FCVTNU(X26, S30))
T(0x1e610120, FCVTNU(W0, D9))
T(0x9e61013a, FCVTNU(X26, D9))
T(0x7e61aaba, FCVTNU(D26, D21))
T(0x6e21aa16, FCVTNU(V22.S4(), V16.S4()))
T(0x1e28010d, FCVTPS(W13, S8))
T(0x9e2803df, FCVTPS(XZR, S30))
T(0x1e6802e9, FCVTPS(W9, D23))
T(0x9e6801f7, FCVTPS(X23, D15))
T(0x5ee1a986, FCVTPS(D6, D12))
T(0x4ea1aa32, FCVTPS(V18.S4(), V17.S4()))
T(0x1e29022b, FCVTPU(W11, S17))
T(0x9e290381, FCVTPU(X1, S28))
T(0x1e690095, FCVTPU(W21, D4))
T(0x9e6902b3, FCVTPU(X19, D21))
T(0x7ea1abbb, FCVTPU(S27, S29))
T(0x6ee1ab06, FCVTPU(V6.D2(), V24.D2()))
T(0x7e61687a, FCVTXN(S26, D3))
T(0x2e61694c, FCVTXN(V12.S2(), V10.D2()))
T(0x1e18c4d6, FCVTZS(W22, S6, 15))
T(0x9e18d131, FCVTZS(X17, S9, 12))
T(0x1e58fd9b, FCVTZS(W27, D12, 1))
T(0x9e5899ee, FCVTZS(X14, D15, 26))
T(0x1e380091, FCVTZS(W17, S4))
T(0x9e380289, FCVTZS(X9, S20))
T(0x1e780117, FCVTZS(W23, D8))
T(0x9e7800f5, FCVTZS(X21, D7))
T(0x5f2fffdb, FCVTZS(S27, S30, 17))
T(0x4f65ff65, FCVTZS(V5.D2(), V27.D2(), 27))
T(0x5ee1b932, FCVTZS(D18, D9))
T(0x4ee1ba41, FCVTZS(V1.D2(), V18.D2()))
T(0x1e19b5d8, FCVTZU(W24, S14, 19))
T(0x9e199462, FCVTZU(X2, S3, 27))
T(0x1e59fca1, FCVTZU(W1, D5, 1))
T(0x9e599bbd, FCVTZU(X29, D29, 26))
T(0x1e3900f6, FCVTZU(W22, S7))
T(0x9e3900b7, FCVTZU(X23, S5))
T(0x1e79031a, FCVTZU(W26, D24))
T(0x9e790248, FCVTZU(X8, D18))
T(0x7f5afd37, FCVTZU(D23, D9, 38))
T(0x2f34fd38, FCVTZU(V24.S2(), V9.S2(), 12))
T(0x7ea1baa3, FCVTZU(S3, S21))
T(0x6ee1b8c1, FCVTZU(V1.D2(), V6.D2()))
T(0x1e3d1999, FDIV(S25, S12, S29))
T(0x1e7e1a4e, FDIV(D14, D18, D30))
T(0x2e2cfe45, FDIV(V5.S2(), V18.S2(), V12.S2()))
T(0x1f114362, FMADD(S2, S27, S17, S16))
T(0x1f482240, FMADD(D0, D18, D8, D8))
T(0x1e234b5f, FMAX(S31, S26, S3))
T(0x1e694894, FMAX(D20, D4, D9))
T(0x4e29f568, FMAX(V8.S4(), V11.S4(), V9.S4()))
T(0x1e2f6a40, FMAXNM(S0, S18, S15))
T(0x1e6d6a99, FMAXNM(D25, D20, D13))
T(0x4e2dc6da, FMAXNM(V26.S4(), V22.S4(), V13.S4()))
T(0x7e30c9b9, FMAXNMP(S25, V13.S2()))
T(0x6e36c794, FMAXNMP(V20.S4(), V28.S4(), V22.S4()))
T(0x6e30c8f6, FMAXNMV(S22, V7.S4()))
T(0x7e30f8dd, FMAXP(S29, V6.S2()))
T(0x6e61f4ab, FMAXP(V11.D2(), V5.D2(), V1.D2()))
T(0x6e30fb85, FMAXV(S5, V28.S4()))
T(0x1e3c5aae, FMIN(S14, S21, S28))
T(0x1e7f58f8, FMIN(D24, D7, D31))
T(0x0eb0f63b, FMIN(V27.S2(), V17.S2(), V16.S2()))
T(0x1e317886, FMINNM(S6, S4, S17))
T(0x1e6e7a5d, FMINNM(D29, D18, D14))
T(0x4ea4c44c, FMINNM(V12.S4(), V2.S4(), V4.S4()))
T(0x7ef0c895, FMINNMP(D21, V4.D2()))
T(0x6efbc4e3, FMINNMP(V3.D2(), V7.D2(), V27.D2()))
T(0x6eb0c93d, FMINNMV(S29, V9.S4()))
T(0x7ef0fa13, FMINP(D19, V16.D2()))
T(0x2eb4f4ac, FMINP(V12.S2(), V5.S2(), V20.S2()))
T(0x6eb0f801, FMINV(S1, V0.S4()))
T(0x5f8219a6, FMLA(S6, S13, V2.S()[2]))
T(0x4fc512a1, FMLA(V1.D2(), V21.D2(), V5.D()[0]))
T(0x4e6bcecf, FMLA(V15.D2(), V22.D2(), V11.D2()))
T(0x5f8a5094, FMLS(S20, S4, V10.S()[0]))
T(0x4fd85b79, FMLS(V25.D2(), V27.D2(), V24.D()[1]))
T(0x0ebacca4, FMLS(V4.S2(), V5.S2(), V26.S2()))
T(0x1e270027, FMOV(S7, W1))
T(0x1e260164, FMOV(W4, S11))
T(0x9e670008, FMOV(D8, X0))
T(0x9eaf03e0, FMOV(V0.D()[1], XZR))
T(0x9e660090, FMOV(X16, D4))
T(0x9eae025f, FMOV(XZR, V18.D()[1]))
T(0x1e204079, FMOV(S25, S3))
T(0x1e6042f8, FMOV(D24, D23))
T(0x1e32f01c, FMOV(S28, FImm8{true, 0b001, 0b0111}))        // -5.75
T(0x1e74901e, FMOV(D30, FImm8{true, 0b010, 0b0100}))        // -10.0
T(0x0f03f51a, FMOV(V26.S2(), FImm8{false, 0b110, 0b1000}))  // 0.75
T(0x6f02f58e, FMOV(V14.D2(), FImm8{false, 0b100, 0b1100}))  // 0.21875
T(0x1f0adaf5, FMSUB(S21, S23, S10, S22))
T(0x1f5da840, FMSUB(D0, D2, D29, D10))
T(0x5fa39bba, FMUL(S26, S29, V3.S()[3]))
T(0x4fb89ad2, FMUL(V18.S4(), V22.S4(), V24.S()[3]))
T(0x1e2b0a3c, FMUL(S28, S17, S11))
T(0x1e720933, FMUL(D19, D9, D18))
T(0x6e7edfa3, FMUL(V3.D2(), V29.D2(), V30.D2()))
T(0x5e32dee6, FMULX(S6, S23, S18))
T(0x0e27deec, FMULX(V12.S2(), V23.S2(), V7.S2()))
T(0x7f879a1f, FMULX(S31, S16, V7.S()[2]))
T(0x6fce9836, FMULX(V22.D2(), V1.D2(), V14.D()[1]))
T(0x1e2142cc, FNEG(S12, S22))
T(0x1e61434b, FNEG(D11, D26))
T(0x6ea0fb90, FNEG(V16.S4(), V28.S4()))
T(0x1f361be5, FNMADD(S5, S31, S22, S6))
T(0x1f7a316d, FNMADD(D13, D11, D26, D12))
T(0x1f3e9957, FNMSUB(S23, S10, S30, S6))
T(0x1f79da66, FNMSUB(D6, D19, D25, D22))
T(0x1e208ab5, FNMUL(S21, S21, S0))
T(0x1e6f89eb, FNMUL(D11, D15, D15))
T(0x5ea1da18, FRECPE(S24, S16))
T(0x0ea1d9df, FRECPE(V31.S2(), V14.S2()))
T(0x5e2dfe37, FRECPS(S23, S17, S13))
T(0x0e29fcec, FRECPS(V12.S2(), V7.S2(), V9.S2()))
T(0x5ee1f998, FRECPX(D24, D12))
T(0x1e264106, FRINTA(S6, S8))
T(0x1e664376, FRINTA(D22, D27))
T(0x6e6188a9, FRINTA(V9.D2(), V5.D2()))
T(0x1e27c216, FRINTI(S22, S16))
T(0x1e67c071, FRINTI(D17, D3))
T(0x6ea19b9d, FRINTI(V29.S4(), V28.S4()))
T(0x1e25413e, FRINTM(S30, S9))
T(0x1e6541a1, FRINTM(D1, D13))
T(0x4e619ad8, FRINTM(V24.D2(), V22.D2()))
T(0x1e244098, FRINTN(S24, S4))
T(0x1e6440b4, FRINTN(D20, D5))
T(0x4e618835, FRINTN(V21.D2(), V1.D2()))
T(0x1e24c188, FRINTP(S8, S12))
T(0x1e64c292, FRINTP(D18, D20))
T(0x0ea18a69, FRINTP(V9.S2(), V19.S2()))
T(0x1e274146, FRINTX(S6, S10))
T(0x1e674333, FRINTX(D19, D25))
T(0x6e619902, FRINTX(V2.D2(), V8.D2()))
T(0x1e25c2b2, FRINTZ(S18, S21))
T(0x1e65c008, FRINTZ(D8, D0))
T(0x0ea19918, FRINTZ(V24.S2(), V8.S2()))
T(0x7ea1dbdb, FRSQRTE(S27, S30))
T(0x6ee1d8df, FRSQRTE(V31.D2(), V6.D2()))
T(0x5ee0ff40, FRSQRTS(D0, D26, D0))
T(0x4eb6fe31, FRSQRTS(V17.S4(), V17.S4(), V22.S4()))
T(0x1e21c204, FSQRT(S4, S16))
T(0x1e61c31c, FSQRT(D28, D24))
T(0x6ea1fa1f, FSQRT(V31.S4(), V16.S4()))
T(0x1e273b28, FSUB(S8, S25, S7))
T(0x1e6139b9, FSUB(D25, D13, D1))
T(0x0eadd6b0, FSUB(V16.S2(), V21.S2(), V13.S2()))
// INS
// INS
T(0x0c407b24, LD1(List{V4.S2()}, X25))
T(0x4c40a891, LD1(List{V17.S4(), V18.S4()}, X4))
T(0x0c406d31, LD1(List{V17.D1(), V18.D1(), V19.D1()}, X9))
T(0x4c402b00, LD1(List{V0.S4(), V1.S4(), V2.S4(), V3.S4()}, X24))
T(0x4cdf72c8, LD1(List{V8.B16()}, X22, POST_INDEXED, 16))
T(0x0cd67504, LD1(List{V4.H4()}, X8, POST_INDEXED, X22))
T(0x0cdfaeb7, LD1(List{V23.D1(), V24.D1()}, X21, POST_INDEXED, 16))
T(0x0cd0a837, LD1(List{V23.S2(), V24.S2()}, X1, POST_INDEXED, X16))
T(0x4cdf6d36, LD1(List{V22.D2(), V23.D2(), V24.D2()}, X9, POST_INDEXED, 48))
T(0x0cdc685b, LD1(List{V27.S2(), V28.S2(), V29.S2()}, X2, POST_INDEXED, X28))
T(0x0cdf2ebc, LD1(List{V28.D1(), V29.D1(), V30.D1(), V31.D1()}, X21, POST_INDEXED, 32))
T(0x0cc0260c, LD1(List{V12.H4(), V13.H4(), V14.H4(), V15.H4()}, X16, POST_INDEXED, X0))
T(0x0d400665, LD1(List{V5.B()}[1], X19))
T(0x0d4041da, LD1(List{V26.H()}[0], X14))
T(0x0d40815b, LD1(List{V27.S()}[0], X10))
T(0x0d408755, LD1(List{V21.D()}[0], X26))
T(0x4ddf0966, LD1(List{V6.B()}[10], X11, POST_INDEXED, 1))
T(0x4dcc1951, LD1(List{V17.B()}[14], X10, POST_INDEXED, X12))
T(0x0ddf58cf, LD1(List{V15.H()}[3], X6, POST_INDEXED, 2))
T(0x0dd14a3d, LD1(List{V29.H()}[1], X17, POST_INDEXED, X17))
T(0x0ddf8072, LD1(List{V18.S()}[0], X3, POST_INDEXED, 4))
T(0x4dcb90bb, LD1(List{V27.S()}[3], X5, POST_INDEXED, X11))
T(0x4ddf8537, LD1(List{V23.D()}[1], X9, POST_INDEXED, 8))
T(0x0dcf8784, LD1(List{V4.D()}[0], X28, POST_INDEXED, X15))
T(0x0d40c0f1, LD1R(List{V17.B8()}, X7))
T(0x0ddfceac, LD1R(List{V12.D1()}, X21, POST_INDEXED, 8))
T(0x4dd5c9c2, LD1R(List{V2.S4()}, X14, POST_INDEXED, X21))
T(0x0c408bc8, LD2(List{V8.S2(), V9.S2()}, X30))
T(0x0cdf842a, LD2(List{V10.H4(), V11.H4()}, X1, POST_INDEXED, 16))
T(0x0cd58678, LD2(List{V24.H4(), V25.H4()}, X19, POST_INDEXED, X21))
T(0x0d60132f, LD2(List{V15.B(), V16.B()}[4], X25))
T(0x4d605156, LD2(List{V22.H(), V23.H()}[6], X10))
T(0x0d609293, LD2(List{V19.S(), V20.S()}[1], X20))
T(0x4d608599, LD2(List{V25.D(), V26.D()}[1], X12))
T(0x4dff0bd6, LD2(List{V22.B(), V23.B()}[10], X30, POST_INDEXED, 2))
T(0x0df90bab, LD2(List{V11.B(), V12.B()}[2], X29, POST_INDEXED, X25))
T(0x4dff42c3, LD2(List{V3.H(), V4.H()}[4], X22, POST_INDEXED, 4))
T(0x4dfa5816, LD2(List{V22.H(), V23.H()}[7], X0, POST_INDEXED, X26))
T(0x4dff9372, LD2(List{V18.S(), V19.S()}[3], X27, POST_INDEXED, 8))
T(0x4de483c0, LD2(List{V0.S(), V1.S()}[2], X30, POST_INDEXED, X4))
T(0x4dff8714, LD2(List{V20.D(), V21.D()}[1], X24, POST_INDEXED, 16))
T(0x4dfa854d, LD2(List{V13.D(), V14.D()}[1], X10, POST_INDEXED, X26))
T(0x4d60ca33, LD2R(List{V19.S4(), V20.S4()}, X17))
T(0x0dffc777, LD2R(List{V23.H4(), V24.H4()}, X27, POST_INDEXED, 4))
T(0x4de9c3cd, LD2R(List{V13.B16(), V14.B16()}, X30, POST_INDEXED, X9))
T(0x0c404032, LD3(List{V18.B8(), V19.B8(), V20.B8()}, X1))
T(0x0cdf4bc8, LD3(List{V8.S2(), V9.S2(), V10.S2()}, X30, POST_INDEXED, 24))
T(0x4ccb4960, LD3(List{V0.S4(), V1.S4(), V2.S4()}, X11, POST_INDEXED, X11))
T(0x0d40217c, LD3(List{V28.B(), V29.B(), V30.B()}[0], X11))
T(0x4d407a38, LD3(List{V24.H(), V25.H(), V26.H()}[7], X17))
T(0x4d40a119, LD3(List{V25.S(), V26.S(), V27.S()}[2], X8))
T(0x0d40a6bb, LD3(List{V27.D(), V28.D(), V29.D()}[0], X21))
T(0x4ddf2bb1, LD3(List{V17.B(), V18.B(), V19.B()}[10], X29, POST_INDEXED, 3))
T(0x4dc13519, LD3(List{V25.B(), V26.B(), V27.B()}[13], X8, POST_INDEXED, X1))
T(0x4ddf6b3f, LD3(List{V31.H(), V0.H(), V1.H()}[5], X25, POST_INDEXED, 6))
T(0x4dc16243, LD3(List{V3.H(), V4.H(), V5.H()}[4], X18, POST_INDEXED, X1))
T(0x4ddfa329, LD3(List{V9.S(), V10.S(), V11.S()}[2], X25, POST_INDEXED, 12))
T(0x4ddab328, LD3(List{V8.S(), V9.S(), V10.S()}[3], X25, POST_INDEXED, X26))
T(0x4ddfa4e4, LD3(List{V4.D(), V5.D(), V6.D()}[1], X7, POST_INDEXED, 24))
T(0x0ddba58c, LD3(List{V12.D(), V13.D(), V14.D()}[0], X12, POST_INDEXED, X27))
T(0x0d40e3b3, LD3R(List{V19.B8(), V20.B8(), V21.B8()}, X29))
T(0x0ddfe2f3, LD3R(List{V19.B8(), V20.B8(), V21.B8()}, X23, POST_INDEXED, 3))
T(0x0ddbe8e4, LD3R(List{V4.S2(), V5.S2(), V6.S2()}, X7, POST_INDEXED, X27))
T(0x4c400a69, LD4(List{V9.S4(), V10.S4(), V11.S4(), V12.S4()}, X19))
T(0x0cdf0bea, LD4(List{V10.S2(), V11.S2(), V12.S2(), V13.S2()}, SP, POST_INDEXED, 32))
T(0x4cd705ad, LD4(List{V13.H8(), V14.H8(), V15.H8(), V16.H8()}, X13, POST_INDEXED, X23))
T(0x0d603b97, LD4(List{V23.B(), V24.B(), V25.B(), V26.B()}[6], X28))
T(0x0d606941, LD4(List{V1.H(), V2.H(), V3.H(), V4.H()}[1], X10))
T(0x0d60a039, LD4(List{V25.S(), V26.S(), V27.S(), V28.S()}[0], X1))
T(0x4d60a4c5, LD4(List{V5.D(), V6.D(), V7.D(), V8.D()}[1], X6))
T(0x0dff2139, LD4(List{V25.B(), V26.B(), V27.B(), V28.B()}[0], X9, POST_INDEXED, 4))
T(0x4df32513, LD4(List{V19.B(), V20.B(), V21.B(), V22.B()}[9], X8, POST_INDEXED, X19))
T(0x0dff7b45, LD4(List{V5.H(), V6.H(), V7.H(), V8.H()}[3], X26, POST_INDEXED, 8))
T(0x0dfa6839, LD4(List{V25.H(), V26.H(), V27.H(), V28.H()}[1], X1, POST_INDEXED, X26))
T(0x4dffa176, LD4(List{V22.S(), V23.S(), V24.S(), V25.S()}[2], X11, POST_INDEXED, 16))
T(0x4de0a125, LD4(List{V5.S(), V6.S(), V7.S(), V8.S()}[2], X9, POST_INDEXED, X0))
T(0x0dffa4ab, LD4(List{V11.D(), V12.D(), V13.D(), V14.D()}[0], X5, POST_INDEXED, 32))
T(0x0dfba784, LD4(List{V4.D(), V5.D(), V6.D(), V7.D()}[0], X28, POST_INDEXED, X27))
T(0x4d60ef82, LD4R(List{V2.D2(), V3.D2(), V4.D2(), V5.D2()}, X28))
T(0x0dffef23, LD4R(List{V3.D1(), V4.D1(), V5.D1(), V6.D1()}, X25, POST_INDEXED, 32))
T(0x4df5e36a, LD4R(List{V10.B16(), V11.B16(), V12.B16(), V13.B16()}, X27, POST_INDEXED, X21))
T(0x2c6dde58, LDNP(S24, S23, X18, -148))
T(0x6c5f8ad5, LDNP(D21, D2, X22, 504))
T(0xac793251, LDNP(Q17, Q12, X18, -224))
T(0x2cf1b345, LDP(S5, S12, X26, POST_INDEXED, -116))
T(0x6cc9489a, LDP(D26, D18, X4, POST_INDEXED, 144))
T(0xace34b69, LDP(Q9, Q18, X27, POST_INDEXED, -928))
T(0x2dca159f, LDP(S31, S5, X12, PRE_INDEXED, 80))
T(0x6df9682d, LDP(D13, D26, X1, PRE_INDEXED, -112))
T(0xadc7566f, LDP(Q15, Q21, X19, PRE_INDEXED, 224))
T(0x2d4efb01, LDP(S1, S30, X24, 116))
T(0x6d710b5a, LDP(D26, D2, X26, -240))
T(0xad74fbb0, LDP(Q16, Q30, X29, -368))
T(0x3c5b76a9, LDR(B9, X21, POST_INDEXED, -73))
T(0x7c5fd798, LDR(H24, X28, POST_INDEXED, -3))
T(0xbc4336b6, LDR(S22, X21, POST_INDEXED, 51))
T(0xfc53b4d5, LDR(D21, X6, POST_INDEXED, -197))
T(0x3cdf571d, LDR(Q29, X24, POST_INDEXED, -11))
T(0x3c5baf77, LDR(B23, X27, PRE_INDEXED, -70))
T(0x7c41bc79, LDR(H25, X3, PRE_INDEXED, 27))
T(0xbc48ecb2, LDR(S18, X5, PRE_INDEXED, 142))
T(0xfc4b1dee, LDR(D14, X15, PRE_INDEXED, 177))
T(0x3cc31c6a, LDR(Q10, X3, PRE_INDEXED, 49))
T(0x3d5a0ef6, LDR(B22, X23, 1667))
T(0x7d5d8dd7, LDR(H23, X14, 3782))
T(0xbd55d41a, LDR(S26, X0, 5588))
T(0xfd58c566, LDR(D6, X11, 12680))
T(0x3dce966e, LDR(Q14, X19, 14928))
T(0x1c8599c0, LDR(S0, -1002696))
T(0x5c8a1ca4, LDR(D4, -965740))
T(0x9cfd90fa, LDR(Q26, -19940))
T(0x3c634a12, LDR(B18, X16, W3, UXTW))
T(0x3c7368e7, LDR(B7, X7, X19, LSL, 0))
T(0x7c646a38, LDR(H24, X17, X4))
T(0xbc727bda, LDR(S26, X30, X18, LSL, 2))
T(0xfc63eb36, LDR(D22, X25, X3, SXTX))
T(0x3ce2ca06, LDR(Q6, X16, W2, SXTW))
T(0x3c4233e6, LDUR(B6, SP, 35))
T(0x7c4d52f1, LDUR(H17, X23, 213))
T(0xbc5be12f, LDUR(S15, X9, -66))
T(0xfc474197, LDUR(D23, X12, 116))
T(0x3cd703db, LDUR(Q27, X30, -144))
T(0x2f9a0354, MLA(V20.S2(), V26.S2(), V26.S()[0]))
T(0x4e7e9643, MLA(V3.H8(), V18.H8(), V30.H8()))
T(0x2f80484e, MLS(V14.S2(), V2.S2(), V0.S()[2]))
T(0x6ebb9572, MLS(V18.S4(), V11.S4(), V27.S4()))
T(0x6e135ec1, MOV(V1.B()[9], V22.B()[11]))
T(0x4e0f1da9, MOV(V9.B()[7], W13))
T(0x5e0e045d, MOV(H29, V2.H()[3]))
T(0x0e043ca1, MOV(W1, V5.S()[0]))
T(0x0e0c3da8, MOV(W8, V13.S()[1]))
T(0x0e143da8, MOV(W8, V13.S()[2]))
T(0x0e1c3da8, MOV(W8, V13.S()[3]))
T(0x4e183d03, MOV(X3, V8.D()[1]))
T(0x4e083df7, MOV(X23, V15.D()[0]))
// MOV
T(0x0f06e58e, MOVI(V14.B8(), 204))
T(0x4f058559, MOVI(V25.H8(), 170))
T(0x0f030565, MOVI(V5.S2(), 107))
T(0x0f05c4dc, MOVI(V28.S2(), 166, MSL, 8))
T(0x2f07e47e, MOVI(D30, RepImm{0b11100011}))       //
T(0x6f03e65b, MOVI(V27.D2(), RepImm{0b01110010}))  //
T(0x0f9e813e, MUL(V30.S2(), V9.S2(), V30.S()[0]))
T(0x4ea59f8e, MUL(V14.S4(), V28.S4(), V5.S4()))
T(0x2e205acd, MVN(V13.B8(), V22.B8()))
T(0x2f0084e1, MVNI(V1.H4(), 7))
T(0x6f026602, MVNI(V2.S4(), 80, LSL, 24))
T(0x2f03c71a, MVNI(V26.S2(), 120, MSL, 8))
T(0x7ee0ba9e, NEG(D30, D20))
T(0x2ea0b9f7, NEG(V23.S2(), V15.S2()))
// NOT
T(0x4ef81f0f, ORN(V15.B16(), V24.B16(), V24.B16()))
T(0x4f03b4e0, ORR(V0.H8(), 103, LSL, 8))
T(0x4f043508, ORR(V8.S4(), 136, LSL, 8))
T(0x4eb21c9c, ORR(V28.B16(), V4.B16(), V18.B16()))
T(0x2e279d77, PMUL(V23.B8(), V11.B8(), V7.B8()))
T(0x4e27e299, PMULL2(V25.H8(), V20.B16(), V7.B16()))
T(0x2eab4048, RADDHN(V8.S2(), V2.D2(), V11.D2()))
T(0x6e605b7e, RBIT(V30.B16(), V27.B16()))
T(0x0e201b37, REV16(V23.B8(), V25.B8()))
T(0x6e60098a, REV32(V10.H8(), V12.H8()))
T(0x0e2009de, REV64(V30.B8(), V14.B8()))
T(0x4f218e4e, RSHRN2(V14.S4(), V18.D2(), 31))
T(0x6e7460f2, RSUBHN2(V18.H8(), V7.S4(), V20.S4()))
T(0x0e377f74, SABA(V20.B8(), V27.B8(), V23.B8()))
T(0x4ea851f6, SABAL2(V22.D2(), V15.S4(), V8.S4()))
T(0x0e777752, SABD(V18.H4(), V26.H4(), V23.H4()))
T(0x0eba7005, SABDL(V5.D2(), V0.S2(), V26.S2()))
T(0x4e2069c4, SADALP(V4.H8(), V14.B16()))
T(0x4e270017, SADDL2(V23.H8(), V0.B16(), V7.B16()))
T(0x0ea028ca, SADDLP(V10.D1(), V6.S2()))
T(0x4e703b2a, SADDLV(S10, V25.H8()))
T(0x0e6311d2, SADDW(V18.S4(), V14.S4(), V3.H4()))
T(0x1e02c782, SCVTF(S2, W28, 15))
T(0x1e42d0e2, SCVTF(D2, W7, 12))
T(0x9e02e80e, SCVTF(S14, X0, 6))
T(0x9e423dda, SCVTF(D26, X14, 49))
T(0x1e2202f3, SCVTF(S19, W23))
T(0x1e6201e7, SCVTF(D7, W15))
T(0x9e22016c, SCVTF(S12, X11))
T(0x9e620316, SCVTF(D22, X24))
T(0x5f34e509, SCVTF(S9, S8, 12))
T(0x4f5ae716, SCVTF(V22.D2(), V24.D2(), 38))
T(0x5e61d946, SCVTF(D6, D10))
T(0x4e61d86b, SCVTF(V11.D2(), V3.D2()))
// SHA1C
// SHA1H
// SHA1M
// SHA1P
// SHA1SU0
// SHA1SU1
// SHA256H
// SHA256H2
// SHA256SU0
// SHA256SU1
T(0x4eb90506, SHADD(V6.S4(), V8.S4(), V25.S4()))
T(0x5f4d5767, SHL(D7, D27, 13))
T(0x4f1f542f, SHL(V15.H8(), V1.H8(), 15))
T(0x2ea13a71, SHLL(V17.D2(), V19.S2(), 32))
T(0x4f0885fd, SHRN2(V29.B16(), V15.H8(), 8))
T(0x0eb42794, SHSUB(V20.S2(), V28.S2(), V20.S2()))
T(0x7f5f54ad, SLI(D13, D5, 31))
T(0x6f09554e, SLI(V14.B16(), V10.B16(), 1))
T(0x0e316452, SMAX(V18.B8(), V2.B8(), V17.B8()))
T(0x4e66a478, SMAXP(V24.H8(), V3.H8(), V6.H8()))
T(0x0e30a9e6, SMAXV(B6, V15.B8()))
T(0x4e276e2a, SMIN(V10.B16(), V17.B16(), V7.B16()))
T(0x4e29ad73, SMINP(V19.B16(), V11.B16(), V9.B16()))
T(0x0e71aac5, SMINV(H5, V22.H4()))
T(0x4f9f2b00, SMLAL2(V0.D2(), V24.S4(), V31.S()[2]))
T(0x4e788037, SMLAL2(V23.S4(), V1.H8(), V24.H8()))
T(0x4f7362b9, SMLSL2(V25.S4(), V21.H8(), V3.H()[3]))
T(0x0e31a0d5, SMLSL(V21.H8(), V6.B8(), V17.B8()))
T(0x0e162fc3, SMOV(W3, V30.H()[5]))
T(0x4e0a2cf2, SMOV(X18, V7.H()[2]))
T(0x0f6ba85c, SMULL(V28.S4(), V2.H4(), V11.H()[6]))
T(0x4e61c2a1, SMULL2(V1.S4(), V21.H8(), V1.H8()))
T(0x5e20794c, SQABS(B12, B10))
T(0x4e607b9b, SQABS(V27.H8(), V28.H8()))
T(0x5eb50df4, SQADD(S20, S15, S21))
T(0x0e370ff4, SQADD(V20.B8(), V31.B8(), V23.B8()))
T(0x5fab3a4e, SQDMLAL(D14, S18, V11.S()[3]))
T(0x4f5b3805, SQDMLAL2(V5.S4(), V0.H8(), V11.H()[5]))
T(0x5e7f90ed, SQDMLAL(S13, H7, H31))
T(0x0ea992b2, SQDMLAL(V18.D2(), V21.S2(), V9.S2()))
T(0x5f867ba2, SQDMLSL(D2, S29, V6.S()[2]))
T(0x4f997118, SQDMLSL2(V24.D2(), V8.S4(), V25.S()[0]))
T(0x5e62b0b2, SQDMLSL(S18, H5, H2))
T(0x0e74b089, SQDMLSL(V9.S4(), V4.H4(), V20.H4()))
T(0x5f5acb3c, SQDMULH(H28, H25, V10.H()[5]))
T(0x4f7bc13d, SQDMULH(V29.H8(), V9.H8(), V11.H()[3]))
T(0x5e6ab724, SQDMULH(H4, H25, H10))
T(0x4ea6b543, SQDMULH(V3.S4(), V10.S4(), V6.S4()))
T(0x5f89b899, SQDMULL(D25, S4, V9.S()[2]))
T(0x0f53b2ee, SQDMULL(V14.S4(), V23.H4(), V3.H()[1]))
T(0x5e60d01a, SQDMULL(S26, H0, H0))
T(0x0eb4d146, SQDMULL(V6.D2(), V10.S2(), V20.S2()))
T(0x7ee07b81, SQNEG(D1, D28))
T(0x2e607a04, SQNEG(V4.H4(), V16.H4()))
T(0x5f47dac8, SQRDMULH(H8, H22, V7.H()[4]))
T(0x0f45db93, SQRDMULH(V19.H4(), V28.H4(), V5.H()[4]))
T(0x7ea3b621, SQRDMULH(S1, S17, S3))
T(0x6ea2b672, SQRDMULH(V18.S4(), V19.S4(), V2.S4()))
T(0x5e7c5ee7, SQRSHL(H7, H23, H28))
T(0x4e655e4b, SQRSHL(V11.H8(), V18.H8(), V5.H8()))
T(0x5f0c9c10, SQRSHRN(B16, H0, 4))
T(0x4f309e99, SQRSHRN2(V25.S4(), V20.D2(), 16))
T(0x7f1f8de7, SQRSHRUN(H7, S15, 1))
T(0x6f178f67, SQRSHRUN2(V7.H8(), V27.S4(), 9))
T(0x5f7977b8, SQSHL(D24, D29, 57))
T(0x4f1e75f3, SQSHL(V19.H8(), V15.H8(), 14))
T(0x5eb24f5d, SQSHL(S29, S26, S18))
T(0x4e7c4c93, SQSHL(V19.H8(), V4.H8(), V28.H8()))
T(0x7f2e66a1, SQSHLU(S1, S21, 14))
T(0x6f4c65a2, SQSHLU(V2.D2(), V13.D2(), 12))
T(0x5f3f950b, SQSHRN(S11, D8, 1))
T(0x4f329646, SQSHRN2(V6.S4(), V18.D2(), 14))
T(0x7f188469, SQSHRUN(H9, S3, 8))
T(0x6f328478, SQSHRUN2(V24.S4(), V3.D2(), 14))
T(0x5e362dae, SQSUB(B14, B13, B22))
T(0x0e3c2c86, SQSUB(V6.B8(), V4.B8(), V28.B8()))
T(0x5ea149fc, SQXTN(S28, D15))
T(0x4e214b24, SQXTN2(V4.B16(), V25.H8()))
T(0x7e61290e, SQXTUN(H14, S8))
T(0x6ea12b96, SQXTUN2(V22.S4(), V28.D2()))
T(0x4eae1673, SRHADD(V19.S4(), V19.S4(), V14.S4()))
T(0x7f794647, SRI(D7, D18, 7))
T(0x6f654787, SRI(V7.D2(), V28.D2(), 27))
T(0x5ee0549e, SRSHL(D30, D4, D0))
T(0x4eba55d2, SRSHL(V18.S4(), V14.S4(), V26.S4()))
T(0x5f712744, SRSHR(D4, D26, 15))
T(0x4f2025f5, SRSHR(V21.S4(), V15.S4(), 32))
T(0x5f7734a9, SRSRA(D9, D5, 9))
T(0x0f3a371a, SRSRA(V26.S2(), V24.S2(), 6))
T(0x5eed44ee, SSHL(D14, D7, D13))
T(0x0e704683, SSHL(V3.H4(), V20.H4(), V16.H4()))
T(0x4f2aa7c3, SSHLL2(V3.D2(), V30.S4(), 10))
T(0x5f5e058d, SSHR(D13, D12, 34))
T(0x4f730496, SSHR(V22.D2(), V4.D2(), 13))
T(0x5f5e152a, SSRA(D10, D9, 34))
T(0x0f21172b, SSRA(V11.S2(), V25.S2(), 31))
T(0x4e24220f, SSUBL2(V15.H8(), V16.B16(), V4.B16()))
T(0x4e3f32a2, SSUBW2(V2.H8(), V21.H8(), V31.B16()))
T(0x0c007a62, ST1(List{V2.S2()}, X19))
T(0x4c00adb7, ST1(List{V23.D2(), V24.D2()}, X13))
T(0x0c006b92, ST1(List{V18.S2(), V19.S2(), V20.S2()}, X28))
T(0x4c0029b8, ST1(List{V24.S4(), V25.S4(), V26.S4(), V27.S4()}, X13))
T(0x0c9f7f60, ST1(List{V0.D1()}, X27, POST_INDEXED, 8))
T(0x0c9f7ebc, ST1(List{V28.D1()}, X21, POST_INDEXED, 8))
T(0x0c9faf06, ST1(List{V6.D1(), V7.D1()}, X24, POST_INDEXED, 16))
T(0x4c93aff5, ST1(List{V21.D2(), V22.D2()}, SP, POST_INDEXED, X19))
T(0x4c9f6398, ST1(List{V24.B16(), V25.B16(), V26.B16()}, X28, POST_INDEXED, 48))
T(0x4c8162ff, ST1(List{V31.B16(), V0.B16(), V1.B16()}, X23, POST_INDEXED, X1))
T(0x0c9f23ee, ST1(List{V14.B8(), V15.B8(), V16.B8(), V17.B8()}, SP, POST_INDEXED, 32))
T(0x4c862148, ST1(List{V8.B16(), V9.B16(), V10.B16(), V11.B16()}, X10, POST_INDEXED, X6))
T(0x0d001c7a, ST1(List{V26.B()}[7], X3))
T(0x0d005b54, ST1(List{V20.H()}[3], X26))
T(0x4d009392, ST1(List{V18.S()}[3], X28))
T(0x4d008509, ST1(List{V9.D()}[1], X8))
T(0x4d9f1246, ST1(List{V6.B()}[12], X18, POST_INDEXED, 1))
T(0x0d8c17f5, ST1(List{V21.B()}[5], SP, POST_INDEXED, X12))
T(0x4d9f53ee, ST1(List{V14.H()}[6], SP, POST_INDEXED, 2))
T(0x0d8f48c4, ST1(List{V4.H()}[1], X6, POST_INDEXED, X15))
T(0x4d9f8185, ST1(List{V5.S()}[2], X12, POST_INDEXED, 4))
T(0x0d8c92bc, ST1(List{V28.S()}[1], X21, POST_INDEXED, X12))
T(0x4d9f86b3, ST1(List{V19.D()}[1], X21, POST_INDEXED, 8))
T(0x4d9c8442, ST1(List{V2.D()}[1], X2, POST_INDEXED, X28))
T(0x4c008a69, ST2(List{V9.S4(), V10.S4()}, X19))
T(0x4c9f8930, ST2(List{V16.S4(), V17.S4()}, X9, POST_INDEXED, 32))
T(0x0c9a8993, ST2(List{V19.S2(), V20.S2()}, X12, POST_INDEXED, X26))
T(0x0d2001ac, ST2(List{V12.B(), V13.B()}[0], X13))
T(0x4d20495c, ST2(List{V28.H(), V29.H()}[5], X10))
T(0x4d2093e4, ST2(List{V4.S(), V5.S()}[3], SP))
T(0x4d208482, ST2(List{V2.D(), V3.D()}[1], X4))
T(0x4dbf0e40, ST2(List{V0.B(), V1.B()}[11], X18, POST_INDEXED, 2))
T(0x0db8085f, ST2(List{V31.B(), V0.B()}[2], X2, POST_INDEXED, X24))
T(0x0dbf4a2d, ST2(List{V13.H(), V14.H()}[1], X17, POST_INDEXED, 4))
T(0x4db1417e, ST2(List{V30.H(), V31.H()}[4], X11, POST_INDEXED, X17))
T(0x0dbf81af, ST2(List{V15.S(), V16.S()}[0], X13, POST_INDEXED, 8))
T(0x0dbf831c, ST2(List{V28.S(), V29.S()}[0], X24, POST_INDEXED, 8))
T(0x0dbf846a, ST2(List{V10.D(), V11.D()}[0], X3, POST_INDEXED, 16))
T(0x0dab85dc, ST2(List{V28.D(), V29.D()}[0], X14, POST_INDEXED, X11))
T(0x0c004a09, ST3(List{V9.S2(), V10.S2(), V11.S2()}, X16))
T(0x4c9f4768, ST3(List{V8.H8(), V9.H8(), V10.H8()}, X27, POST_INDEXED, 48))
T(0x0c944918, ST3(List{V24.S2(), V25.S2(), V26.S2()}, X8, POST_INDEXED, X20))
T(0x0d003f80, ST3(List{V0.B(), V1.B(), V2.B()}[7], X28))
T(0x0d007306, ST3(List{V6.H(), V7.H(), V8.H()}[2], X24))
T(0x0d00b131, ST3(List{V17.S(), V18.S(), V19.S()}[1], X9))
T(0x4d00a5f8, ST3(List{V24.D(), V25.D(), V26.D()}[1], X15))
T(0x0d9f27c1, ST3(List{V1.B(), V2.B(), V3.B()}[1], X30, POST_INDEXED, 3))
T(0x4d992bb2, ST3(List{V18.B(), V19.B(), V20.B()}[10], X29, POST_INDEXED, X25))
T(0x0d9f785d, ST3(List{V29.H(), V30.H(), V31.H()}[3], X2, POST_INDEXED, 6))
T(0x4d8b726b, ST3(List{V11.H(), V12.H(), V13.H()}[6], X19, POST_INDEXED, X11))
T(0x4d9fa342, ST3(List{V2.S(), V3.S(), V4.S()}[2], X26, POST_INDEXED, 12))
T(0x4d80b206, ST3(List{V6.S(), V7.S(), V8.S()}[3], X16, POST_INDEXED, X0))
T(0x4d9fa5de, ST3(List{V30.D(), V31.D(), V0.D()}[1], X14, POST_INDEXED, 24))
T(0x4d8ba6d7, ST3(List{V23.D(), V24.D(), V25.D()}[1], X22, POST_INDEXED, X11))
T(0x0c00034f, ST4(List{V15.B8(), V16.B8(), V17.B8(), V18.B8()}, X26))
T(0x4c9f038c, ST4(List{V12.B16(), V13.B16(), V14.B16(), V15.B16()}, X28, POST_INDEXED, 64))
T(0x4c800719, ST4(List{V25.H8(), V26.H8(), V27.H8(), V28.H8()}, X24, POST_INDEXED, X0))
T(0x0d2021a8, ST4(List{V8.B(), V9.B(), V10.B(), V11.B()}[0], X13))
T(0x4d2062cd, ST4(List{V13.H(), V14.H(), V15.H(), V16.H()}[4], X22))
T(0x0d20b146, ST4(List{V6.S(), V7.S(), V8.S(), V9.S()}[1], X10))
T(0x4d20a6f5, ST4(List{V21.D(), V22.D(), V23.D(), V24.D()}[1], X23))
T(0x0dbf2d56, ST4(List{V22.B(), V23.B(), V24.B(), V25.B()}[3], X10, POST_INDEXED, 4))
T(0x4da631df, ST4(List{V31.B(), V0.B(), V1.B(), V2.B()}[12], X14, POST_INDEXED, X6))
T(0x0dbf7a76, ST4(List{V22.H(), V23.H(), V24.H(), V25.H()}[3], X19, POST_INDEXED, 8))
T(0x0dbb698e, ST4(List{V14.H(), V15.H(), V16.H(), V17.H()}[1], X12, POST_INDEXED, X27))
T(0x4dbfb37f, ST4(List{V31.S(), V0.S(), V1.S(), V2.S()}[3], X27, POST_INDEXED, 16))
T(0x4dadb3d1, ST4(List{V17.S(), V18.S(), V19.S(), V20.S()}[3], X30, POST_INDEXED, X13))
T(0x4dbfa5b3, ST4(List{V19.D(), V20.D(), V21.D(), V22.D()}[1], X13, POST_INDEXED, 32))
T(0x4db5a7cf, ST4(List{V15.D(), V16.D(), V17.D(), V18.D()}[1], X30, POST_INDEXED, X21))
T(0x2c29149a, STNP(S26, S5, X4, -184))
T(0x6c229316, STNP(D22, D4, X24, -472))
T(0xac3bc3c8, STNP(Q8, Q16, X30, -144))
T(0x2cacdf66, STP(S6, S23, X27, POST_INDEXED, -156))
T(0x6c826f4f, STP(D15, D27, X26, POST_INDEXED, 32))
T(0xac97955a, STP(Q26, Q5, X10, POST_INDEXED, 752))
T(0x2da7ba37, STP(S23, S14, X17, PRE_INDEXED, -196))
T(0x6d8bcbce, STP(D14, D18, X30, PRE_INDEXED, 184))
T(0xad8b4ba6, STP(Q6, Q18, X29, PRE_INDEXED, 352))
T(0x2d1f7434, STP(S20, S29, X1, 248))
T(0x6d3bb5d8, STP(D24, D13, X14, -72))
T(0xad09088a, STP(Q10, Q2, X4, 288))
T(0x3c066467, STR(B7, X3, POST_INDEXED, 102))
T(0x7c070723, STR(H3, X25, POST_INDEXED, 112))
T(0xbc13175a, STR(S26, X26, POST_INDEXED, -207))
T(0xfc1be536, STR(D22, X9, POST_INDEXED, -66))
T(0x3c99b56b, STR(Q11, X11, POST_INDEXED, -101))
T(0x3c002d49, STR(B9, X10, PRE_INDEXED, 2))
T(0x7c158e09, STR(H9, X16, PRE_INDEXED, -168))
T(0xbc06bc8d, STR(S13, X4, PRE_INDEXED, 107))
T(0xfc080eae, STR(D14, X21, PRE_INDEXED, 128))
T(0x3c8e7ed9, STR(Q25, X22, PRE_INDEXED, 231))
T(0x3d275492, STR(B18, X4, 2517))
T(0x7d0b4265, STR(H5, X19, 1440))
T(0xbd0d2595, STR(S21, X12, 3364))
T(0xfd237a73, STR(D19, X19, 18160))
T(0x3db4a5f5, STR(Q21, X15, 53904))
T(0x3c3e693c, STR(B28, X9, X30, LSL, 0))
T(0x3c3b6ac5, STR(B5, X22, X27, LSL, 0))
T(0x7c36faf0, STR(H16, X23, X22, SXTX, 1))
T(0xbc27f838, STR(S24, X1, X7, SXTX, 2))
T(0xfc29db51, STR(D17, X26, W9, SXTW, 3))
T(0x3cbfea8f, STR(Q15, X20, XZR, SXTX))
T(0x3c0441c8, STUR(B8, X14, 68))
T(0x7c00b0d7, STUR(H23, X6, 11))
T(0xbc0d117d, STUR(S29, X11, 209))
T(0xfc1f03c0, STUR(D0, X30, -16))
T(0x3c9753f0, STUR(Q16, SP, -139))
T(0x7eeb84f9, SUB(D25, D7, D11))
T(0x6e708714, SUB(V20.H8(), V24.H8(), V16.H8()))
T(0x4e766323, SUBHN2(V3.H8(), V25.S4(), V22.S4()))
T(0x5e203935, SUQADD(B21, B9))
T(0x4e203b33, SUQADD(V19.B16(), V25.B16()))
// SXTL
T(0x0e0c20db, TBL(V27.B8(), List{V6.B16(), V7.B16()}, V12.B8()))
T(0x4e1d43ab, TBL(V11.B16(), List{V29.B16(), V30.B16(), V31.B16()}, V29.B16()))
T(0x0e07634f, TBL(V15.B8(), List{V26.B16(), V27.B16(), V28.B16(), V29.B16()}, V7.B8()))
T(0x0e0603b9, TBL(V25.B8(), List{V29.B16()}, V6.B8()))
T(0x0e05317a, TBX(V26.B8(), List{V11.B16(), V12.B16()}, V5.B8()))
T(0x4e0150ca, TBX(V10.B16(), List{V6.B16(), V7.B16(), V8.B16()}, V1.B16()))
T(0x4e0e7190, TBX(V16.B16(), List{V12.B16(), V13.B16(), V14.B16(), V15.B16()}, V14.B16()))
T(0x4e1b1333, TBX(V19.B16(), List{V25.B16()}, V27.B16()))
T(0x4e0829e3, TRN1(V3.B16(), V15.B16(), V8.B16()))
T(0x4ecc6b24, TRN2(V4.D2(), V25.D2(), V12.D2()))
T(0x2e697f5d, UABA(V29.H4(), V26.H4(), V9.H4()))
T(0x2e36519e, UABAL(V30.H8(), V12.B8(), V22.B8()))
T(0x6e6975e0, UABD(V0.H8(), V15.H8(), V9.H8()))
T(0x2e2e718a, UABDL(V10.H8(), V12.B8(), V14.B8()))
T(0x6ea069b1, UADALP(V17.D2(), V13.S4()))
T(0x2e6d0349, UADDL(V9.S4(), V26.H4(), V13.H4()))
T(0x6e602bfc, UADDLP(V28.S4(), V31.H8()))
T(0x6e703b6d, UADDLV(S13, V27.H8()))
T(0x2e781352, UADDW(V18.S4(), V26.S4(), V24.H4()))
T(0x1e03ec95, UCVTF(S21, W4, 5))
T(0x1e43fd36, UCVTF(D22, W9, 1))
T(0x9e03a27b, UCVTF(S27, X19, 24))
T(0x9e43e9c4, UCVTF(D4, X14, 6))
T(0x1e230096, UCVTF(S22, W4))
T(0x1e630076, UCVTF(D22, W3))
T(0x9e2302c8, UCVTF(S8, X22))
T(0x9e6302cd, UCVTF(D13, X22))
T(0x7f2ce5a2, UCVTF(S2, S13, 20))
T(0x6f4be788, UCVTF(V8.D2(), V28.D2(), 53))
T(0x7e21d87f, UCVTF(S31, S3))
T(0x2e21da7d, UCVTF(V29.S2(), V19.S2()))
T(0x2e7b0674, UHADD(V20.H4(), V19.H4(), V27.H4()))
T(0x6ea9277f, UHSUB(V31.S4(), V27.S4(), V9.S4()))
T(0x6e7a6658, UMAX(V24.H8(), V18.H8(), V26.H8()))
T(0x2e23a513, UMAXP(V19.B8(), V8.B8(), V3.B8()))
T(0x2e70a9b5, UMAXV(H21, V13.H4()))
T(0x6e7d6ef2, UMIN(V18.H8(), V23.H8(), V29.H8()))
T(0x2e6eae4e, UMINP(V14.H4(), V18.H4(), V14.H4()))
T(0x2e71abe6, UMINV(H6, V31.H4()))
T(0x6fb820fa, UMLAL2(V26.D2(), V7.S4(), V24.S()[1]))
T(0x6ebc83ab, UMLAL2(V11.D2(), V29.S4(), V28.S4()))
T(0x2f5c61cf, UMLSL(V15.S4(), V14.H4(), V12.H()[1]))
T(0x6e6aa2e2, UMLSL2(V2.S4(), V23.H8(), V10.H8()))
T(0x0e0f3fb8, UMOV(W24, V29.B()[7]))
// UMOV
T(0x6f62a05c, UMULL2(V28.S4(), V2.H8(), V2.H()[2]))
T(0x6e6cc3b0, UMULL2(V16.S4(), V29.H8(), V12.H8()))
T(0x7ea40f68, UQADD(S8, S27, S4))
T(0x6eac0e8f, UQADD(V15.S4(), V20.S4(), V12.S4()))
T(0x7e2a5df5, UQRSHL(B21, B15, B10))
T(0x6ef55fc9, UQRSHL(V9.D2(), V30.D2(), V21.D2()))
T(0x7f0b9db4, UQRSHRN(B20, H13, 5))
T(0x2f159d7d, UQRSHRN(V29.H4(), V11.S4(), 11))
T(0x7f6c755c, UQSHL(D28, D10, 44))
T(0x6f6175ec, UQSHL(V12.D2(), V15.D2(), 33))
T(0x7eef4ff4, UQSHL(D20, D31, D15))
T(0x6e3d4f2e, UQSHL(V14.B16(), V25.B16(), V29.B16()))
T(0x7f1f94d2, UQSHRN(H18, S6, 1))
T(0x6f3397e4, UQSHRN2(V4.S4(), V31.D2(), 13))
T(0x7ee12cad, UQSUB(D13, D5, D1))
T(0x2e712ff3, UQSUB(V19.H4(), V31.H4(), V17.H4()))
T(0x7e614b06, UQXTN(H6, S24))
T(0x6e2149ec, UQXTN2(V12.B16(), V15.H8()))
T(0x0ea1c849, URECPE(V9.S2(), V2.S2()))
T(0x6eb51740, URHADD(V0.S4(), V26.S4(), V21.S4()))
T(0x7eeb57f8, URSHL(D24, D31, D11))
T(0x6e335531, URSHL(V17.B16(), V9.B16(), V19.B16()))
T(0x7f65253d, URSHR(D29, D9, 27))
T(0x2f102566, URSHR(V6.H4(), V11.H4(), 16))
T(0x2ea1cb59, URSQRTE(V25.S2(), V26.S2()))
T(0x7f54345f, URSRA(D31, D2, 44))
T(0x2f1b345f, URSRA(V31.H4(), V2.H4(), 5))
T(0x7ef94448, USHL(D8, D2, D25))
T(0x6ea14621, USHL(V1.S4(), V17.S4(), V1.S4()))
T(0x2f33a5a1, USHLL(V1.D2(), V13.S2(), 19))
T(0x7f5405d0, USHR(D16, D14, 44))
T(0x6f450505, USHR(V5.D2(), V8.D2(), 59))
T(0x7ea038c1, USQADD(S1, S6))
T(0x2e203b60, USQADD(V0.B8(), V27.B8()))
T(0x7f4616d2, USRA(D18, D22, 58))
T(0x2f1a1713, USRA(V19.H4(), V24.H4(), 6))
T(0x2e3f226e, USUBL(V14.H8(), V19.B8(), V31.B8()))
T(0x6e7a33a0, USUBW2(V0.S4(), V29.S4(), V26.H8()))
// UXTL
T(0x4e1b1a1f, UZP1(V31.B16(), V16.B16(), V27.B16()))
T(0x4ecc597b, UZP2(V27.D2(), V11.D2(), V12.D2()))
T(0x0e212af7, XTN(V23.B8(), V23.H8()))
T(0x4e853928, ZIP1(V8.S4(), V9.S4(), V5.S4()))
T(0x0e977a78, ZIP2(V24.S2(), V19.S2(), V23.S2()))
